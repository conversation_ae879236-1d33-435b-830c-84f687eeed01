# MaixCAM形状检测完整技术文档

## 项目概述

本项目实现了从OpenCV到MaixCAM平台的完整形状检测算法移植，支持实时检测图像中的外边框和内部几何形状（三角形、正方形、圆形），并提供精确的高度测量功能。

**项目信息：**
- 作者：Alex (Engineer)
- 版权：米醋电子工作室
- 平台：MaixCAM + MaixPy
- 语言：Python 3

## 核心算法原理

### 1. 外边框检测算法

**算法流程：**
1. **图像预处理** → 高斯模糊降噪
2. **边缘检测** → Canny算法提取边缘
3. **轮廓查找** → 查找所有外部轮廓
4. **多边形近似** → 将轮廓近似为多边形
5. **矩形筛选** → 筛选四边形且满足宽高比条件的轮廓

**关键参数：**
- 最小面积阈值：2000像素
- 宽高比范围：0.5 ~ 2.0
- 多边形近似精度：轮廓周长的2%

### 2. 内部形状识别算法

**形状分类策略：**

#### 三角形识别
- **顶点数量**：3个
- **边长比例**：三边长度比例均 > 0.8（接近等边）
- **高度计算**：最高点到最低点的距离

#### 正方形识别  
- **顶点数量**：4个
- **宽高比**：0.9 ~ 1.1（接近1:1）
- **高度计算**：边界框高度

#### 圆形识别
- **顶点数量**：> 4个
- **圆形度**：4π×面积/周长² > 0.8
- **高度计算**：直径（2×半径）

### 3. 高度测量算法

**测量原理：**
- 三角形/正方形：计算边界框或关键点间距离
- 圆形：计算最小外接圆直径
- 单位：像素（可根据实际标定转换为物理单位）

## 完整源代码

### 主程序文件：maixcam_shape_detection_advanced.py

```python
#!/usr/bin/env python3
"""
MaixCAM高级形状检测程序
使用OpenCV兼容层实现完整的形状检测功能

功能：
1. 完整的边缘检测和轮廓查找
2. 精确的形状识别和分类
3. 高度测量和可视化
4. 性能优化和错误处理

作者：Alex (Engineer)
版权：米醋电子工作室
"""

import math
import numpy as np
from maix import camera, display, image, app, time

# 尝试导入OpenCV进行高级图像处理
try:
    import cv2
    OPENCV_AVAILABLE = True
    print("OpenCV可用，启用高级图像处理功能")
except ImportError:
    OPENCV_AVAILABLE = False
    print("OpenCV不可用，使用基础图像处理功能")

class AdvancedShapeDetector:
    """高级形状检测器类"""
    
    def __init__(self, width=640, height=480):
        """初始化检测器"""
        self.width = width
        self.height = height
        self.cam = camera.Camera(width, height)
        self.disp = display.Display()
        
        # 检测参数
        self.min_area = 2000
        self.min_inner_area = 300
        self.gaussian_kernel = 5
        self.canny_low = 50
        self.canny_high = 150
        
        # 性能统计
        self.frame_count = 0
        self.start_time = time.ticks_ms()
        
        print("MaixCAM高级形状检测器初始化完成")
        print(f"分辨率: {width}x{height}")
        print(f"OpenCV支持: {'是' if OPENCV_AVAILABLE else '否'}")
    
    def maix_to_cv2(self, maix_img):
        """将MaixPy图像转换为OpenCV格式"""
        if not OPENCV_AVAILABLE:
            return None
            
        # 获取图像数据
        img_data = maix_img.to_bytes()
        
        if maix_img.format() == image.Format.FMT_RGB888:
            # RGB格式转换
            np_array = np.frombuffer(img_data, dtype=np.uint8)
            np_array = np_array.reshape((maix_img.height(), maix_img.width(), 3))
            cv_img = cv2.cvtColor(np_array, cv2.COLOR_RGB2BGR)
        elif maix_img.format() == image.Format.FMT_GRAYSCALE:
            # 灰度格式转换
            np_array = np.frombuffer(img_data, dtype=np.uint8)
            cv_img = np_array.reshape((maix_img.height(), maix_img.width()))
        else:
            # 其他格式先转换为RGB
            rgb_img = maix_img.to_format(image.Format.FMT_RGB888)
            return self.maix_to_cv2(rgb_img)
        
        return cv_img
    
    def cv2_to_maix(self, cv_img):
        """将OpenCV图像转换为MaixPy格式"""
        if not OPENCV_AVAILABLE:
            return None
            
        if len(cv_img.shape) == 3:  # 彩色图像
            rgb_img = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
            return image.Image(cv_img.shape[1], cv_img.shape[0], 
                              image.Format.FMT_RGB888, rgb_img.tobytes())
        else:  # 灰度图像
            return image.Image(cv_img.shape[1], cv_img.shape[0], 
                              image.Format.FMT_GRAYSCALE, cv_img.tobytes())
    
    def detect_outer_rectangle_opencv(self, cv_gray):
        """使用OpenCV检测外边框"""
        # 高斯模糊减少噪声
        blurred = cv2.GaussianBlur(cv_gray, (self.gaussian_kernel, self.gaussian_kernel), 0)
        
        # 边缘检测
        edges = cv2.Canny(blurred, self.canny_low, self.canny_high)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        max_area = 0
        best_rect = None
        x, y, w, h = None, None, None, None
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < self.min_area:
                continue
                
            # 近似轮廓为多边形
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # 检查是否为四边形
            if len(approx) == 4:
                rect_x, rect_y, rect_w, rect_h = cv2.boundingRect(approx)
                aspect_ratio = float(rect_w) / rect_h
                
                # 过滤过于狭长的形状
                if 0.5 < aspect_ratio < 2.0 and area > max_area:
                    max_area = area
                    best_rect = approx.reshape(-1, 2)  # 转换为(N,2)格式
                    x, y, w, h = rect_x, rect_y, rect_w, rect_h
        
        return best_rect, x, y, w, h
```

### 内部形状检测核心算法

```python
    def detect_inner_shapes_opencv(self, inner_roi, inner_x, inner_y):
        """使用OpenCV检测内部形状"""
        detected_shapes = []
        
        if inner_roi.size == 0:
            return detected_shapes
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(inner_roi, (self.gaussian_kernel, self.gaussian_kernel), 0)
        
        # 边缘检测
        edges = cv2.Canny(blurred, self.canny_low, self.canny_high)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < self.min_inner_area:
                continue
            
            # 轮廓近似
            perimeter = cv2.arcLength(cnt, True)
            epsilon = 0.03 * perimeter
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            vertices = len(approx)
            
            # 计算形状中心
            M = cv2.moments(cnt)
            if M["m00"] == 0:
                continue
            
            cX = int(M["m10"] / M["m00"]) + inner_x
            cY = int(M["m01"] / M["m00"]) + inner_y
            
            # 形状判断
            shape = "none"
            shape_height = 0
            top_point = (0, 0)
            bottom_point = (0, 0)
            
            if vertices == 3:  # 三角形
                # 检查是否为等边三角形
                side_lengths = []
                for i in range(3):
                    p1 = approx[i][0]
                    p2 = approx[(i+1)%3][0]
                    length = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                    side_lengths.append(length)
                
                max_side = max(side_lengths)
                ratios = [s/max_side for s in side_lengths]
                
                if all(r > 0.8 for r in ratios):
                    shape = "Triangle"
                    # 找到最高和最低点
                    points = approx.reshape(-1, 2)
                    top_idx = np.argmin(points[:, 1])
                    bottom_idx = np.argmax(points[:, 1])
                    
                    top_point = (points[top_idx][0] + inner_x, points[top_idx][1] + inner_y)
                    bottom_point = (points[bottom_idx][0] + inner_x, points[bottom_idx][1] + inner_y)
                    shape_height = int(np.sqrt((top_point[0] - bottom_point[0])**2 + 
                                             (top_point[1] - bottom_point[1])**2))
            
            elif vertices == 4:  # 四边形
                x_cnt, y_cnt, w_cnt, h_cnt = cv2.boundingRect(approx)
                aspect = w_cnt / h_cnt
                if 0.9 < aspect < 1.1:
                    shape = "Square"
                    top_point = (cX, y_cnt + inner_y)
                    bottom_point = (cX, y_cnt + h_cnt + inner_y)
                    shape_height = h_cnt
            
            else:  # 可能是圆形
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                if circularity > 0.8:
                    shape = "Circle"
                    (circle_x, circle_y), radius = cv2.minEnclosingCircle(cnt)
                    circle_x = int(circle_x) + inner_x
                    circle_y = int(circle_y) + inner_y
                    radius = int(radius)
                    
                    top_point = (circle_x, circle_y - radius)
                    bottom_point = (circle_x, circle_y + radius)
                    shape_height = radius * 2
            
            # 保存形状信息
            if shape != "none":
                detected_shapes.append({
                    "name": shape,
                    "contour": cnt,
                    "height": shape_height,
                    "top_point": top_point,
                    "bottom_point": bottom_point,
                    "center": (cX, cY)
                })
        
        return detected_shapes
```

### 基础检测算法（无OpenCV依赖）

```python
    def detect_outer_rectangle_basic(self, maix_img):
        """使用基础方法检测外边框"""
        # 转换为灰度图
        if maix_img.format() != image.Format.FMT_GRAYSCALE:
            gray_img = maix_img.to_format(image.Format.FMT_GRAYSCALE)
        else:
            gray_img = maix_img
        
        # 使用色块检测
        thresholds = [[0, 100, -128, 127, -128, 127]]
        blobs = gray_img.find_blobs(thresholds, 
                                   pixels_threshold=self.min_area,
                                   area_threshold=self.min_area)
        
        max_area = 0
        best_rect = None
        x, y, w, h = None, None, None, None
        
        for blob in blobs:
            blob_area = blob[4]
            if blob_area > max_area:
                max_area = blob_area
                x, y, w, h = blob[0], blob[1], blob[2], blob[3]
                best_rect = [(x, y), (x + w, y), (x + w, y + h), (x, y + h)]
        
        return best_rect, x, y, w, h
```

### 图像处理和可视化

```python
    def process_frame(self, frame):
        """处理单帧图像"""
        display_img = frame.copy()
        
        if OPENCV_AVAILABLE:
            # 使用OpenCV处理
            cv_img = self.maix_to_cv2(frame)
            if cv_img is None:
                return display_img
            
            # 转换为灰度图
            if len(cv_img.shape) == 3:
                cv_gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
            else:
                cv_gray = cv_img
            
            # 检测外边框
            best_rect, x, y, w, h = self.detect_outer_rectangle_opencv(cv_gray)
        else:
            # 使用基础方法
            best_rect, x, y, w, h = self.detect_outer_rectangle_basic(frame)
        
        # 显示状态
        status_text = "none"
        status_color = image.Color.from_rgb(255, 0, 0)
        
        if best_rect is not None:
            status_text = "have"
            status_color = image.Color.from_rgb(0, 255, 0)
            
            # 绘制矩形
            for i in range(len(best_rect)):
                p1 = best_rect[i]
                p2 = best_rect[(i + 1) % len(best_rect)]
                display_img.draw_line(int(p1[0]), int(p1[1]), 
                                    int(p2[0]), int(p2[1]), status_color, 2)
            
            # 处理内部区域
            if OPENCV_AVAILABLE and x is not None:
                inner_x = x + int(w * 0.1)
                inner_y = y + int(h * 0.1)
                inner_w = int(w * 0.8)
                inner_h = int(h * 0.8)
                
                # 提取内部区域
                cv_img = self.maix_to_cv2(frame)
                if cv_img is not None:
                    if len(cv_img.shape) == 3:
                        cv_gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
                    else:
                        cv_gray = cv_img
                    
                    inner_roi = cv_gray[inner_y:inner_y+inner_h, inner_x:inner_x+inner_w]
                    
                    # 检测内部形状
                    detected_shapes = self.detect_inner_shapes_opencv(inner_roi, inner_x, inner_y)
                    
                    # 绘制形状
                    for shape_info in detected_shapes:
                        # 绘制高度线
                        display_img.draw_line(shape_info["top_point"][0], shape_info["top_point"][1],
                                            shape_info["bottom_point"][0], shape_info["bottom_point"][1],
                                            image.Color.from_rgb(255, 255, 0), 2)
                        
                        # 显示形状名称和高度
                        display_img.draw_string(shape_info["center"][0] - 30, shape_info["center"][1],
                                              shape_info["name"], image.Color.from_rgb(255, 0, 0))
                        
                        mid_x = (shape_info["top_point"][0] + shape_info["bottom_point"][0]) // 2
                        mid_y = (shape_info["top_point"][1] + shape_info["bottom_point"][1]) // 2
                        display_img.draw_string(mid_x + 20, mid_y,
                                              f"{shape_info['height']}px", 
                                              image.Color.from_rgb(255, 255, 0))
                        
                        # 标记端点
                        display_img.draw_circle(shape_info["top_point"][0], shape_info["top_point"][1],
                                              5, image.Color.from_rgb(0, 255, 255), -1)
                        display_img.draw_circle(shape_info["bottom_point"][0], shape_info["bottom_point"][1],
                                              5, image.Color.from_rgb(0, 255, 255), -1)
        
        # 添加状态和性能信息
        display_img.draw_string(10, 30, status_text, status_color)
        
        # 计算FPS
        current_time = time.ticks_ms()
        elapsed = current_time - self.start_time
        if elapsed > 0:
            fps = self.frame_count * 1000 / elapsed
            display_img.draw_string(10, 60, f"FPS: {fps:.1f}", 
                                  image.Color.from_rgb(255, 255, 255))
        
        return display_img
```

### 主循环和程序入口

```python
    def run(self):
        """运行主循环"""
        print("开始高级形状检测...")
        
        try:
            while not app.need_exit():
                frame = self.cam.read()
                if frame is None:
                    continue
                
                processed_frame = self.process_frame(frame)
                self.disp.show(processed_frame)
                
                self.frame_count += 1
                
                if self.frame_count % 60 == 0:
                    current_time = time.ticks_ms()
                    elapsed = current_time - self.start_time
                    fps = self.frame_count * 1000 / elapsed if elapsed > 0 else 0
                    print(f"已处理 {self.frame_count} 帧, 平均FPS: {fps:.1f}")
                
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行出错: {e}")
        finally:
            print("程序结束")


def main():
    """主函数"""
    print("=" * 50)
    print("MaixCAM高级形状检测程序")
    print("版权所有：米醋电子工作室")
    print("=" * 50)
    
    detector = AdvancedShapeDetector(width=640, height=480)
    detector.run()


if __name__ == "__main__":
    main()
```

## 算法内部数学运算详解

### 📋 核心变量对照表

**为了帮助理解，这里列出所有重要变量的含义和作用：**

#### 图像处理变量
| 变量名 | 含义 | 单位 | 作用 | 典型值 |
|--------|------|------|------|--------|
| `image` | 输入图像矩阵 | 像素值 | 存储图像的亮度信息 | 0-255 |
| `height, width` | 图像尺寸 | 像素 | 定义图像大小 | 640×480 |
| `kernel` | 卷积核矩阵 | 权重 | 图像滤波的权重 | 0-1之间 |
| `sigma` | 高斯标准差 | 无量纲 | 控制模糊程度 | 1.0 |
| `pixel_value` | 像素亮度值 | 0-255 | 单个像素的亮度 | 128 |

#### 边缘检测变量
| 变量名 | 含义 | 单位 | 作用 | 典型值 |
|--------|------|------|------|--------|
| `Gx, Gy` | x,y方向梯度 | 亮度差 | 检测边缘方向 | -255到+255 |
| `magnitude` | 梯度幅值 | 边缘强度 | 表示边缘明显程度 | 0-360 |
| `direction` | 梯度方向 | 角度(度) | 边缘的方向角 | 0-180° |
| `canny_low` | Canny低阈值 | 亮度差 | 弱边缘判断标准 | 50 |
| `canny_high` | Canny高阈值 | 亮度差 | 强边缘判断标准 | 150 |

#### 形状检测变量
| 变量名 | 含义 | 单位 | 作用 | 典型值 |
|--------|------|------|------|--------|
| `contour` | 轮廓点集 | 像素坐标 | 描述形状边界 | [(x1,y1),(x2,y2)...] |
| `area` | 轮廓面积 | 像素² | 形状大小 | 2000 |
| `perimeter` | 轮廓周长 | 像素 | 形状边界长度 | 200 |
| `approx` | 近似多边形 | 像素坐标 | 简化后的形状 | 3-8个顶点 |
| `circularity` | 圆形度 | 0-1 | 接近圆形的程度 | 0.8 |

#### 距离转换变量
| 变量名 | 含义 | 单位 | 作用 | 典型值 |
|--------|------|------|------|--------|
| `pixel_distance` | 像素距离 | 像素 | 图像中的测量距离 | 150 |
| `physical_distance` | 物理距离 | 毫米 | 现实中的实际距离 | 30.0 |
| `pixels_per_mm` | 像素密度 | 像素/毫米 | 图像分辨率 | 5.0 |
| `fx, fy` | 相机焦距 | 像素 | 相机放大倍数 | 640 |
| `cx, cy` | 主点坐标 | 像素 | 图像中心位置 | (320,240) |
| `Z` | 物体距离 | 毫米 | 物体到相机距离 | 300 |

#### 标定参数变量
| 变量名 | 含义 | 单位 | 作用 | 典型值 |
|--------|------|------|------|--------|
| `reference_distance` | 标定距离 | 毫米 | 标定时的参考距离 | 300 |
| `actual_length_mm` | 参考物体长度 | 毫米 | 已知物体的真实尺寸 | 20.0 |
| `scale_factor` | 距离缩放因子 | 无量纲 | 深度校正系数 | 1.33 |
| `uncertainty` | 测量不确定度 | 毫米 | 测量误差范围 | ±0.2 |

### 1. 图像预处理算法数学原理

#### 高斯模糊算法详细数学运算

**二维高斯函数公式解释：**
```
G(x,y) = (1/(2πσ²)) * e^(-((x-μx)²+(y-μy)²)/(2σ²))

变量说明：
- G(x,y)：位置(x,y)处的高斯权重值，用于图像模糊
- x, y：相对于核心中心的坐标偏移量（像素位置）
- μx, μy：高斯分布的中心位置（通常为0,0，即核心中心）
- σ（sigma）：标准差，控制模糊程度，σ越大模糊越强
- π：圆周率3.14159...
- e：自然对数底数2.71828...
```

**具体计算步骤详解：**
```python
# 1. 生成5×5高斯核
def generate_gaussian_kernel(size=5, sigma=1.0):
    """
    生成高斯模糊核心矩阵

    参数说明：
    - size: 核心大小（5表示5×5矩阵），必须为奇数
    - sigma: 标准差，控制模糊强度，1.0为中等模糊

    返回：
    - kernel: 5×5的权重矩阵，用于图像卷积运算
    """
    kernel = np.zeros((size, size))  # 创建空的5×5矩阵
    center = size // 2               # 计算中心位置 = 2（对于5×5矩阵）

    # 计算每个位置的高斯权重值
    for i in range(size):            # i: 行索引，从0到4
        for j in range(size):        # j: 列索引，从0到4
            # 计算相对于中心的偏移距离
            x = i - center           # x: 行偏移，范围-2到+2
            y = j - center           # y: 列偏移，范围-2到+2

            # 高斯函数计算每个位置的权重
            # 距离中心越近，权重越大；距离越远，权重越小
            distance_squared = x**2 + y**2                    # 距离平方
            gaussian_value = (1/(2*np.pi*sigma**2)) * \       # 归一化系数
                           np.exp(-(distance_squared)/(2*sigma**2))  # 指数衰减

            kernel[i, j] = gaussian_value

    # 归一化处理：确保所有权重之和为1
    total_weight = np.sum(kernel)    # 计算所有权重的总和
    kernel = kernel / total_weight   # 每个权重除以总和
    return kernel

# 2. 卷积运算（图像模糊处理）
def apply_gaussian_blur(image, kernel):
    """
    对图像应用高斯模糊

    参数说明：
    - image: 输入的灰度图像矩阵（二维数组）
    - kernel: 5×5高斯权重矩阵

    功能：减少图像噪声，为边缘检测做准备
    """
    height, width = image.shape      # 获取图像尺寸
    result = np.zeros_like(image)    # 创建与原图相同大小的结果矩阵

    # 遍历图像的每个像素（边缘2像素除外，因为5×5核需要周围像素）
    for i in range(2, height-2):     # i: 当前处理的行，从第2行到倒数第3行
        for j in range(2, width-2):  # j: 当前处理的列，从第2列到倒数第3列

            # 5×5窗口卷积计算
            pixel_value = 0          # 新像素值累加器

            # 遍历5×5高斯核的每个位置
            for ki in range(5):      # ki: 核的行索引，0到4
                for kj in range(5):  # kj: 核的列索引，0到4

                    # 卷积运算核心：
                    # 取当前像素周围5×5区域的像素值
                    image_pixel = image[i-2+ki, j-2+kj]  # 原图像素值
                    kernel_weight = kernel[ki, kj]        # 对应的高斯权重

                    # 加权累加：像素值 × 权重
                    pixel_value += image_pixel * kernel_weight

            # 将计算结果存储到结果图像
            result[i, j] = pixel_value

    return result

# 卷积运算的物理意义：
# 每个新像素 = 周围像素的加权平均值
# 权重由高斯函数决定：距离越近权重越大，距离越远权重越小
# 结果：图像变得平滑，噪声被抑制
```

**数值示例（σ=1.0的5×5高斯核权重分布）：**
```
权重矩阵说明：
[0.003  0.013  0.022  0.013  0.003]  ← 顶行：距离中心远，权重小
[0.013  0.059  0.097  0.059  0.013]  ← 次行：距离中心近，权重大
[0.022  0.097  0.159  0.097  0.022]  ← 中行：中心权重最大(0.159)
[0.013  0.059  0.097  0.059  0.013]  ← 次行：对称分布
[0.003  0.013  0.022  0.013  0.003]  ← 底行：距离中心远，权重小

权重特点：
- 中心权重0.159最大，占主导地位
- 四个角落权重0.003最小，影响微弱
- 总和 = 1.0，保证图像亮度不变
- 对称分布，保证模糊效果均匀
```

#### Canny边缘检测详细数学运算

**1. Sobel算子梯度计算详解：**
```python
# Sobel算子定义（用于检测边缘）
Gx = [[-1, 0, 1],    # x方向（水平）梯度检测算子
      [-2, 0, 2],    # 中间行权重加倍，增强检测效果
      [-1, 0, 1]]    # 检测垂直边缘（左暗右亮或左亮右暗）

Gy = [[-1, -2, -1],  # y方向（垂直）梯度检测算子
      [ 0,  0,  0],  # 中间行为0，形成水平分割线
      [ 1,  2,  1]]  # 检测水平边缘（上暗下亮或上亮下暗）

# Sobel算子的物理意义：
# Gx: 检测图像中的垂直边缘（亮度从左到右的变化）
# Gy: 检测图像中的水平边缘（亮度从上到下的变化）
# 权重-2和+2：加强中心行/列的影响，提高边缘检测精度

# 梯度计算过程
def calculate_gradients(image):
    """
    计算图像的x和y方向梯度

    参数说明：
    - image: 输入的灰度图像（已经过高斯模糊）

    返回：
    - Gx_result: x方向梯度图像（检测垂直边缘）
    - Gy_result: y方向梯度图像（检测水平边缘）

    功能：找出图像中亮度变化剧烈的区域（边缘候选）
    """
    height, width = image.shape          # 获取图像尺寸
    Gx_result = np.zeros_like(image)     # 创建x方向梯度结果矩阵
    Gy_result = np.zeros_like(image)     # 创建y方向梯度结果矩阵

    # 遍历图像每个像素（边缘1像素除外，因为3×3算子需要周围像素）
    for i in range(1, height-1):         # i: 当前行，从第1行到倒数第2行
        for j in range(1, width-1):      # j: 当前列，从第1列到倒数第2列

            # x方向梯度计算（检测垂直边缘）
            gx = 0                       # x方向梯度累加器
            for ki in range(3):          # ki: Sobel核的行索引，0到2
                for kj in range(3):      # kj: Sobel核的列索引，0到2
                    # 卷积运算：图像像素值 × Sobel权重
                    pixel_value = image[i-1+ki, j-1+kj]  # 3×3窗口内的像素值
                    sobel_weight = Gx[ki][kj]             # 对应的Sobel权重
                    gx += pixel_value * sobel_weight      # 加权累加

            Gx_result[i, j] = gx         # 存储x方向梯度值

            # y方向梯度计算（检测水平边缘）
            gy = 0                       # y方向梯度累加器
            for ki in range(3):          # 同样的3×3窗口遍历
                for kj in range(3):
                    pixel_value = image[i-1+ki, j-1+kj]  # 相同的像素值
                    sobel_weight = Gy[ki][kj]             # 使用Gy权重
                    gy += pixel_value * sobel_weight      # 加权累加

            Gy_result[i, j] = gy         # 存储y方向梯度值

    return Gx_result, Gy_result

# 梯度值的含义：
# Gx > 0: 从左到右亮度增加（左暗右亮的垂直边缘）
# Gx < 0: 从左到右亮度减少（左亮右暗的垂直边缘）
# Gy > 0: 从上到下亮度增加（上暗下亮的水平边缘）
# Gy < 0: 从上到下亮度减少（上亮下暗的水平边缘）
# |Gx|或|Gy|越大，边缘越明显
```

**2. 梯度幅值和方向计算详解：**
```python
def calculate_magnitude_direction(Gx, Gy):
    """
    从x和y方向梯度计算边缘强度和方向

    参数说明：
    - Gx: x方向梯度矩阵（检测垂直边缘的强度）
    - Gy: y方向梯度矩阵（检测水平边缘的强度）

    返回：
    - magnitude: 梯度幅值（边缘强度，值越大边缘越明显）
    - direction: 梯度方向（边缘的方向角度，0-180度）

    功能：将x、y两个方向的梯度合成为边缘的强度和方向
    """

    # 梯度幅值计算：M = √(Gx² + Gy²)
    # 物理意义：计算边缘的总强度（类似向量的模长）
    magnitude = np.sqrt(Gx**2 + Gy**2)

    # 数学解释：
    # - Gx²: x方向梯度的平方（垂直边缘强度的平方）
    # - Gy²: y方向梯度的平方（水平边缘强度的平方）
    # - Gx² + Gy²: 总的梯度能量
    # - √(Gx² + Gy²): 总的边缘强度

    # 梯度方向计算：θ = arctan(Gy/Gx)
    # 物理意义：计算边缘垂直方向的角度
    direction = np.arctan2(Gy, Gx) * 180 / np.pi

    # 数学解释：
    # - arctan2(Gy, Gx): 计算向量(Gx, Gy)的角度，范围-180°到+180°
    # - * 180 / np.pi: 将弧度转换为角度
    # - 结果是边缘法线方向（垂直于边缘的方向）

    # 角度标准化：将负角度转换为正角度
    # 原因：边缘方向只需要0-180°，因为180°和0°表示同一条边缘
    direction[direction < 0] += 180

    # 角度量化为四个主要方向（用于后续非极大值抑制）
    # 0°（水平）、45°（右上-左下对角）、90°（垂直）、135°（左上-右下对角）

    return magnitude, direction

# 梯度幅值和方向的物理意义：
# magnitude[i,j] = 像素(i,j)处的边缘强度
#   - 值为0：该处没有边缘
#   - 值越大：边缘越明显
#   - 典型范围：0-255（对于8位图像）
#
# direction[i,j] = 像素(i,j)处边缘的方向角
#   - 0°：水平边缘（上下亮度不同）
#   - 45°：右上-左下对角边缘
#   - 90°：垂直边缘（左右亮度不同）
#   - 135°：左上-右下对角边缘
```

**3. 非极大值抑制数学过程：**
```python
def non_maximum_suppression(magnitude, direction):
    height, width = magnitude.shape
    suppressed = np.zeros_like(magnitude)

    for i in range(1, height-1):
        for j in range(1, width-1):
            angle = direction[i, j]

            # 根据梯度方向确定比较的邻域像素
            if (0 <= angle < 22.5) or (157.5 <= angle <= 180):
                # 水平方向：比较左右像素
                neighbors = [magnitude[i, j-1], magnitude[i, j+1]]
            elif 22.5 <= angle < 67.5:
                # 45°方向：比较对角线像素
                neighbors = [magnitude[i-1, j+1], magnitude[i+1, j-1]]
            elif 67.5 <= angle < 112.5:
                # 垂直方向：比较上下像素
                neighbors = [magnitude[i-1, j], magnitude[i+1, j]]
            else:  # 112.5 <= angle < 157.5
                # 135°方向：比较对角线像素
                neighbors = [magnitude[i-1, j-1], magnitude[i+1, j+1]]

            # 如果当前像素是局部最大值，则保留
            if magnitude[i, j] >= max(neighbors):
                suppressed[i, j] = magnitude[i, j]

    return suppressed
```

**4. 双阈值检测和边缘连接：**
```python
def double_threshold_and_tracking(suppressed, low_threshold=50, high_threshold=150):
    height, width = suppressed.shape
    edges = np.zeros_like(suppressed)

    # 分类像素
    strong_edges = (suppressed >= high_threshold)  # 强边缘
    weak_edges = ((suppressed >= low_threshold) &
                  (suppressed < high_threshold))   # 弱边缘

    # 标记强边缘
    edges[strong_edges] = 255

    # 边缘连接：弱边缘连接到强边缘
    def is_connected_to_strong_edge(i, j, visited):
        if visited[i, j] or not weak_edges[i, j]:
            return False

        visited[i, j] = True

        # 检查8邻域
        for di in [-1, 0, 1]:
            for dj in [-1, 0, 1]:
                ni, nj = i + di, j + dj
                if (0 <= ni < height and 0 <= nj < width):
                    if strong_edges[ni, nj]:  # 连接到强边缘
                        return True
                    elif (weak_edges[ni, nj] and not visited[ni, nj] and
                          is_connected_to_strong_edge(ni, nj, visited)):
                        return True
        return False

    visited = np.zeros_like(suppressed, dtype=bool)
    for i in range(height):
        for j in range(width):
            if weak_edges[i, j] and is_connected_to_strong_edge(i, j, visited):
                edges[i, j] = 255

    return edges
```

### 2. 轮廓分析算法数学详解

#### 轮廓查找算法（Suzuki-Abe算法）

**算法数学原理：**
```python
def find_contours_detailed(binary_image):
    """
    详细的轮廓查找算法实现
    基于Suzuki-Abe边界跟踪算法
    """
    height, width = binary_image.shape
    contours = []
    visited = np.zeros_like(binary_image, dtype=bool)

    # 8邻域方向编码
    directions = [(-1, -1), (-1, 0), (-1, 1), (0, 1),
                  (1, 1), (1, 0), (1, -1), (0, -1)]

    def trace_contour(start_x, start_y):
        """跟踪单个轮廓"""
        contour = []
        x, y = start_x, start_y
        direction = 0  # 初始搜索方向

        while True:
            contour.append((x, y))
            visited[y, x] = True

            # 寻找下一个边界点
            found_next = False
            for i in range(8):
                # 从当前方向开始搜索
                search_dir = (direction + i) % 8
                dx, dy = directions[search_dir]
                nx, ny = x + dx, y + dy

                # 检查边界条件和像素值
                if (0 <= nx < width and 0 <= ny < height and
                    binary_image[ny, nx] == 255):
                    x, y = nx, ny
                    direction = (search_dir + 6) % 8  # 更新搜索方向
                    found_next = True
                    break

            if not found_next or (x == start_x and y == start_y):
                break

        return contour

    # 扫描图像寻找轮廓起点
    for y in range(height):
        for x in range(width):
            if (binary_image[y, x] == 255 and not visited[y, x]):
                contour = trace_contour(x, y)
                if len(contour) > 3:  # 过滤太小的轮廓
                    contours.append(contour)

    return contours
```

#### 多边形近似算法（Douglas-Peucker）详细数学实现

**算法核心数学原理：**
```python
def douglas_peucker_detailed(points, epsilon):
    """
    Douglas-Peucker多边形简化算法详细实现

    数学原理：
    1. 连接起点和终点形成基线
    2. 找到距离基线最远的点
    3. 如果距离大于阈值epsilon，递归处理两段
    4. 否则，用直线段代替整个曲线段
    """

    def point_to_line_distance(point, line_start, line_end):
        """
        计算点到直线的距离
        数学公式：d = |ax + by + c| / √(a² + b²)
        其中直线方程为 ax + by + c = 0
        """
        x0, y0 = point
        x1, y1 = line_start
        x2, y2 = line_end

        # 直线方程系数计算
        # (y2-y1)x - (x2-x1)y + (x2-x1)y1 - (y2-y1)x1 = 0
        a = y2 - y1
        b = x1 - x2
        c = (x2 - x1) * y1 - (y2 - y1) * x1

        # 点到直线距离
        distance = abs(a * x0 + b * y0 + c) / math.sqrt(a**2 + b**2)
        return distance

    def recursive_simplify(start_idx, end_idx):
        """递归简化线段"""
        if end_idx - start_idx <= 1:
            return [start_idx, end_idx]

        # 找到距离基线最远的点
        max_distance = 0
        max_index = start_idx

        for i in range(start_idx + 1, end_idx):
            distance = point_to_line_distance(
                points[i], points[start_idx], points[end_idx]
            )
            if distance > max_distance:
                max_distance = distance
                max_index = i

        # 判断是否需要分割
        if max_distance > epsilon:
            # 递归处理两段
            left_result = recursive_simplify(start_idx, max_index)
            right_result = recursive_simplify(max_index, end_idx)

            # 合并结果（去除重复点）
            return left_result[:-1] + right_result
        else:
            # 用直线段代替
            return [start_idx, end_idx]

    if len(points) < 3:
        return points

    # 执行简化
    simplified_indices = recursive_simplify(0, len(points) - 1)
    simplified_points = [points[i] for i in simplified_indices]

    return simplified_points

# 实际使用示例
def approximate_polygon(contour, epsilon_ratio=0.02):
    """
    轮廓多边形近似
    epsilon = epsilon_ratio * 轮廓周长
    """
    # 计算轮廓周长
    perimeter = 0
    for i in range(len(contour)):
        p1 = contour[i]
        p2 = contour[(i + 1) % len(contour)]
        perimeter += math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

    # 计算epsilon阈值
    epsilon = epsilon_ratio * perimeter

    # 执行多边形近似
    approximated = douglas_peucker_detailed(contour, epsilon)

    return approximated, epsilon, perimeter
```

#### 轮廓面积计算（Shoelace公式）

**数学公式详解：**
```python
def calculate_contour_area_detailed(contour):
    """
    使用Shoelace公式计算轮廓面积

    数学公式：
    Area = (1/2) * |Σ(xi * yi+1 - xi+1 * yi)|
    其中 i 从 0 到 n-1，且 xn = x0, yn = y0
    """
    n = len(contour)
    if n < 3:
        return 0

    area = 0
    for i in range(n):
        j = (i + 1) % n
        # Shoelace公式的核心计算
        area += contour[i][0] * contour[j][1]  # xi * yi+1
        area -= contour[j][0] * contour[i][1]  # xi+1 * yi

    area = abs(area) / 2.0
    return area

# 数值示例：正方形面积计算
square_contour = [(0, 0), (100, 0), (100, 100), (0, 100)]
area = calculate_contour_area_detailed(square_contour)
print(f"正方形面积: {area}")  # 输出: 10000.0
```

### 3. 形状分类算法数学详解

#### 三角形识别算法详细数学运算

```python
def triangle_classification_detailed(contour_points):
    """
    三角形识别的完整数学分析
    """
    if len(contour_points) != 3:
        return False, None

    p1, p2, p3 = contour_points

    # 1. 边长计算（欧几里得距离）
    def euclidean_distance(point1, point2):
        """
        欧几里得距离公式：d = √((x₂-x₁)² + (y₂-y₁)²)
        """
        dx = point2[0] - point1[0]
        dy = point2[1] - point1[1]
        return math.sqrt(dx**2 + dy**2)

    side_a = euclidean_distance(p1, p2)  # 边a
    side_b = euclidean_distance(p2, p3)  # 边b
    side_c = euclidean_distance(p3, p1)  # 边c

    # 2. 三角形有效性检查（三角形不等式）
    def is_valid_triangle(a, b, c):
        """
        三角形不等式：任意两边之和大于第三边
        a + b > c AND b + c > a AND a + c > b
        """
        return (a + b > c) and (b + c > a) and (a + c > b)

    if not is_valid_triangle(side_a, side_b, side_c):
        return False, "Invalid triangle"

    # 3. 等边三角形判断
    def is_equilateral_triangle(a, b, c, tolerance=0.1):
        """
        等边三角形判断：三边长度比例接近1:1:1
        使用相对误差：|a-b|/max(a,b) < tolerance
        """
        max_side = max(a, b, c)
        ratio_ab = abs(a - b) / max_side
        ratio_bc = abs(b - c) / max_side
        ratio_ca = abs(c - a) / max_side

        return (ratio_ab < tolerance and
                ratio_bc < tolerance and
                ratio_ca < tolerance)

    # 4. 三角形面积计算（海伦公式）
    def triangle_area_heron(a, b, c):
        """
        海伦公式：Area = √(s(s-a)(s-b)(s-c))
        其中 s = (a+b+c)/2 为半周长
        """
        s = (a + b + c) / 2  # 半周长
        area = math.sqrt(s * (s - a) * (s - b) * (s - c))
        return area

    # 5. 三角形高度计算
    def triangle_height_calculation(area, base):
        """
        三角形高度公式：h = 2 * Area / base
        """
        return 2 * area / base

    # 执行分析
    is_equilateral = is_equilateral_triangle(side_a, side_b, side_c)
    area = triangle_area_heron(side_a, side_b, side_c)

    # 选择最长边作为底边计算高度
    max_side = max(side_a, side_b, side_c)
    height = triangle_height_calculation(area, max_side)

    # 6. 内角计算（余弦定理）
    def calculate_angles(a, b, c):
        """
        余弦定理：cos(C) = (a² + b² - c²) / (2ab)
        角度C是边c的对角
        """
        angle_A = math.acos((b**2 + c**2 - a**2) / (2 * b * c))
        angle_B = math.acos((a**2 + c**2 - b**2) / (2 * a * c))
        angle_C = math.acos((a**2 + b**2 - c**2) / (2 * a * b))

        # 转换为度数
        return (math.degrees(angle_A),
                math.degrees(angle_B),
                math.degrees(angle_C))

    angles = calculate_angles(side_a, side_b, side_c)

    result = {
        'is_triangle': True,
        'is_equilateral': is_equilateral,
        'sides': [side_a, side_b, side_c],
        'area': area,
        'height': height,
        'angles': angles,
        'perimeter': side_a + side_b + side_c
    }

    return True, result
```

#### 正方形识别算法详细数学运算

```python
def square_classification_detailed(contour_points):
    """
    正方形识别的完整数学分析
    """
    if len(contour_points) != 4:
        return False, None

    # 1. 边长计算
    def calculate_all_sides(points):
        """计算四边形的四条边长"""
        sides = []
        for i in range(4):
            p1 = points[i]
            p2 = points[(i + 1) % 4]
            side_length = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            sides.append(side_length)
        return sides

    sides = calculate_all_sides(contour_points)

    # 2. 对角线长度计算
    def calculate_diagonals(points):
        """计算四边形的两条对角线长度"""
        diagonal1 = math.sqrt((points[2][0] - points[0][0])**2 +
                             (points[2][1] - points[0][1])**2)
        diagonal2 = math.sqrt((points[3][0] - points[1][0])**2 +
                             (points[3][1] - points[1][1])**2)
        return diagonal1, diagonal2

    diag1, diag2 = calculate_diagonals(contour_points)

    # 3. 正方形判断条件
    def is_square_conditions(sides, diag1, diag2, tolerance=0.1):
        """
        正方形判断条件：
        1. 四边长度相等
        2. 两对角线长度相等
        3. 对角线长度 = 边长 * √2
        """
        # 条件1：四边长度相等
        avg_side = sum(sides) / 4
        side_variance = sum((s - avg_side)**2 for s in sides) / 4
        sides_equal = side_variance < (avg_side * tolerance)**2

        # 条件2：对角线长度相等
        diag_diff = abs(diag1 - diag2)
        diagonals_equal = diag_diff < avg_side * tolerance

        # 条件3：对角线与边长关系
        expected_diagonal = avg_side * math.sqrt(2)
        actual_diagonal = (diag1 + diag2) / 2
        diagonal_ratio_correct = (abs(actual_diagonal - expected_diagonal) <
                                 expected_diagonal * tolerance)

        return sides_equal and diagonals_equal and diagonal_ratio_correct

    # 4. 内角计算（向量夹角）
    def calculate_internal_angles(points):
        """
        计算四边形内角
        使用向量夹角公式：cos(θ) = (u·v) / (|u||v|)
        """
        angles = []
        for i in range(4):
            # 三个连续点
            p1 = points[(i - 1) % 4]
            p2 = points[i]
            p3 = points[(i + 1) % 4]

            # 两个向量
            v1 = (p1[0] - p2[0], p1[1] - p2[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])

            # 向量点积
            dot_product = v1[0] * v2[0] + v1[1] * v2[1]

            # 向量模长
            mag_v1 = math.sqrt(v1[0]**2 + v1[1]**2)
            mag_v2 = math.sqrt(v2[0]**2 + v2[1]**2)

            # 夹角计算
            if mag_v1 > 0 and mag_v2 > 0:
                cos_angle = dot_product / (mag_v1 * mag_v2)
                # 限制cos值范围，避免数值误差
                cos_angle = max(-1, min(1, cos_angle))
                angle = math.acos(cos_angle)
                angles.append(math.degrees(angle))
            else:
                angles.append(0)

        return angles

    # 执行分析
    is_square = is_square_conditions(sides, diag1, diag2)
    angles = calculate_internal_angles(contour_points)
    area = (sum(sides) / 4) ** 2  # 正方形面积 = 边长²

    result = {
        'is_square': is_square,
        'sides': sides,
        'diagonals': [diag1, diag2],
        'area': area,
        'side_length': sum(sides) / 4,
        'angles': angles,
        'perimeter': sum(sides)
    }

    return is_square, result
```

#### 圆形识别算法详细数学运算

```python
def circle_classification_detailed(contour_points):
    """
    圆形识别的完整数学分析
    """

    # 1. 轮廓周长计算
    def calculate_perimeter(points):
        """计算轮廓周长"""
        perimeter = 0
        for i in range(len(points)):
            p1 = points[i]
            p2 = points[(i + 1) % len(points)]
            perimeter += math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
        return perimeter

    # 2. 轮廓面积计算（Shoelace公式）
    def calculate_area_shoelace(points):
        """使用Shoelace公式计算面积"""
        n = len(points)
        area = 0
        for i in range(n):
            j = (i + 1) % n
            area += points[i][0] * points[j][1]
            area -= points[j][0] * points[i][1]
        return abs(area) / 2.0

    # 3. 圆形度计算
    def calculate_circularity(area, perimeter):
        """
        圆形度公式：C = 4π × Area / Perimeter²

        理论值：
        - 完美圆形：C = 1.0
        - 正方形：C = π/4 ≈ 0.785
        - 等边三角形：C ≈ 0.604
        """
        if perimeter == 0:
            return 0
        circularity = 4 * math.pi * area / (perimeter ** 2)
        return circularity

    # 4. 最小外接圆计算（Welzl算法简化版）
    def minimum_enclosing_circle(points):
        """
        计算最小外接圆
        返回圆心坐标和半径
        """
        # 简化实现：使用边界框中心作为近似圆心
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]

        center_x = (min(x_coords) + max(x_coords)) / 2
        center_y = (min(y_coords) + max(y_coords)) / 2

        # 计算到圆心的最大距离作为半径
        max_distance = 0
        for point in points:
            distance = math.sqrt((point[0] - center_x)**2 + (point[1] - center_y)**2)
            max_distance = max(max_distance, distance)

        return (center_x, center_y), max_distance

    # 5. 圆形拟合质量评估
    def circle_fitting_quality(points, center, radius):
        """
        评估点集与圆的拟合质量
        计算每个点到圆周的距离方差
        """
        distances = []
        for point in points:
            # 点到圆心的距离
            dist_to_center = math.sqrt((point[0] - center[0])**2 +
                                     (point[1] - center[1])**2)
            # 点到圆周的距离
            dist_to_circle = abs(dist_to_center - radius)
            distances.append(dist_to_circle)

        # 计算距离方差
        mean_distance = sum(distances) / len(distances)
        variance = sum((d - mean_distance)**2 for d in distances) / len(distances)

        return variance, distances

    # 执行分析
    perimeter = calculate_perimeter(contour_points)
    area = calculate_area_shoelace(contour_points)
    circularity = calculate_circularity(area, perimeter)

    center, radius = minimum_enclosing_circle(contour_points)
    fitting_variance, fitting_distances = circle_fitting_quality(
        contour_points, center, radius
    )

    # 圆形判断条件
    is_circle = (circularity > 0.8 and fitting_variance < radius * 0.1)

    # 理论圆形参数计算
    theoretical_radius = math.sqrt(area / math.pi)  # 从面积推算半径
    theoretical_perimeter = 2 * math.pi * theoretical_radius

    result = {
        'is_circle': is_circle,
        'circularity': circularity,
        'area': area,
        'perimeter': perimeter,
        'center': center,
        'radius': radius,
        'fitting_variance': fitting_variance,
        'theoretical_radius': theoretical_radius,
        'theoretical_perimeter': theoretical_perimeter,
        'diameter': 2 * radius
    }

    return is_circle, result
```

### 4. 像素到物理距离转换详细数学原理

#### 相机标定数学基础详解

**针孔相机模型完整解释：**
```
相机成像的数学模型基于针孔相机原理：
真实世界坐标 (X, Y, Z) → 图像像素坐标 (u, v)

投影方程详解：
u = fx * (X/Z) + cx
v = fy * (Y/Z) + cy

变量详细说明：

输入变量（真实世界）：
- X: 物体在世界坐标系中的x坐标（毫米）
- Y: 物体在世界坐标系中的y坐标（毫米）
- Z: 物体到相机镜头的距离（毫米）

输出变量（图像坐标）：
- u: 物体在图像中的水平像素坐标（像素）
- v: 物体在图像中的垂直像素坐标（像素）

相机参数（需要标定获得）：
- fx: x方向焦距（像素单位），表示1毫米在x方向对应多少像素
- fy: y方向焦距（像素单位），表示1毫米在y方向对应多少像素
- cx: 主点x坐标（像素），通常是图像中心的x坐标
- cy: 主点y坐标（像素），通常是图像中心的y坐标

物理意义：
- fx, fy越大：相机"放大倍数"越高，同样距离的物体在图像中越大
- Z越大：物体越远，在图像中越小
- (X/Z), (Y/Z)：物体相对于相机的角度位置
```

**相机内参矩阵详解：**
```
K = [fx  0  cx]    ← 相机内参矩阵（3×3）
    [0  fy  cy]
    [0   0   1]

矩阵元素说明：
- K[0,0] = fx: x方向焦距，控制水平方向的缩放
- K[1,1] = fy: y方向焦距，控制垂直方向的缩放
- K[0,2] = cx: 主点x坐标，图像中心的水平位置
- K[1,2] = cy: 主点y坐标，图像中心的垂直位置
- K[0,1] = 0: 倾斜参数，通常为0（假设像素是矩形的）
- K[2,0] = K[2,1] = 0: 齐次坐标填充
- K[2,2] = 1: 齐次坐标标准化

使用方法：
世界坐标点 [X, Y, Z] 通过矩阵运算转换为图像坐标 [u, v, 1]
[u']   [fx  0  cx] [X]
[v'] = [0  fy  cy] [Y]  然后 u = u'/Z, v = v'/Z
[Z ]   [0   0   1] [Z]
```

#### 距离标定算法详细实现

```python
class PixelToPhysicalConverter:
    """像素到物理距离转换器"""

    def __init__(self):
        # 标定参数
        self.pixels_per_mm = None  # 每毫米像素数
        self.reference_distance = None  # 参考距离（mm）
        self.camera_height = None  # 相机高度（mm）
        self.camera_angle = 0  # 相机倾斜角度（度）

        # 相机内参（需要标定获得）
        self.fx = None  # x方向焦距
        self.fy = None  # y方向焦距
        self.cx = None  # 主点x坐标
        self.cy = None  # 主点y坐标

    def calibrate_with_reference_object(self, pixel_length, actual_length_mm):
        """
        使用参考物体进行标定（最简单的标定方法）

        参数详解:
            pixel_length: 参考物体在图像中的像素长度
                        - 单位：像素（pixel）
                        - 含义：物体在图像中占多少个像素点
                        - 获取方法：测量图像中物体的长度
                        - 示例：100像素

            actual_length_mm: 参考物体的实际物理长度
                            - 单位：毫米（mm）
                            - 含义：物体在现实中的真实尺寸
                            - 获取方法：用尺子测量实际物体
                            - 示例：20毫米

        数学原理详解:
            pixels_per_mm = pixel_length / actual_length_mm

            计算过程：
            - 如果20mm的物体在图像中占100像素
            - 那么每1mm对应 100÷20 = 5像素
            - pixels_per_mm = 5.0 （每毫米5像素）

            物理意义：
            - pixels_per_mm：图像分辨率，表示每毫米对应多少像素
            - 值越大：图像越"精细"，能看到更小的细节
            - 值越小：图像越"粗糙"，同样的物体在图像中更小

        应用场景：
            - 有标准尺寸的参考物体（如硬币、尺子）
            - 相机距离固定
            - 需要快速简单的标定
        """
        # 执行标定计算
        self.pixels_per_mm = pixel_length / actual_length_mm

        # 输出标定结果
        print(f"标定完成：每毫米 {self.pixels_per_mm:.4f} 像素")
        print(f"标定精度：1像素 = {1/self.pixels_per_mm:.4f} 毫米")

        return self.pixels_per_mm

    def calibrate_with_known_distance(self, image_width_pixels,
                                    actual_width_mm, distance_mm):
        """
        使用已知距离进行标定

        参数:
            image_width_pixels: 图像宽度（像素）
            actual_width_mm: 实际视野宽度（毫米）
            distance_mm: 相机到物体的距离（毫米）

        数学原理:
            视野角度: θ = 2 * arctan(actual_width_mm / (2 * distance_mm))
            焦距: fx = image_width_pixels / (2 * tan(θ/2))
        """
        # 计算视野角度
        theta = 2 * math.atan(actual_width_mm / (2 * distance_mm))

        # 计算焦距
        self.fx = image_width_pixels / (2 * math.tan(theta / 2))
        self.fy = self.fx  # 假设像素是正方形

        # 主点通常在图像中心
        self.cx = image_width_pixels / 2
        self.cy = image_width_pixels / 2  # 假设正方形图像

        # 计算每毫米像素数
        self.pixels_per_mm = self.fx / distance_mm
        self.reference_distance = distance_mm

        print(f"相机标定完成:")
        print(f"焦距 fx: {self.fx:.2f} pixels")
        print(f"参考距离: {distance_mm} mm")
        print(f"每毫米像素数: {self.pixels_per_mm:.4f}")

        return self.fx, self.fy, self.cx, self.cy

    def pixel_to_mm_simple(self, pixel_distance):
        """
        简单像素到毫米转换（平面测量）

        参数详解:
            pixel_distance: 图像中测量的像素距离
                          - 单位：像素（pixel）
                          - 含义：两点之间的像素距离
                          - 计算方法：√((x2-x1)² + (y2-y1)²)
                          - 示例：150像素

        返回值:
            physical_distance: 对应的物理距离
                             - 单位：毫米（mm）
                             - 含义：真实世界中的距离

        数学公式详解:
            physical_distance = pixel_distance / pixels_per_mm

            计算过程示例：
            - 如果标定结果是每毫米5像素（pixels_per_mm = 5.0）
            - 图像中测量距离是150像素（pixel_distance = 150）
            - 物理距离 = 150 ÷ 5.0 = 30毫米

            物理意义：
            - 将图像中的像素距离转换为现实中的毫米距离
            - 前提：物体与参考物体在同一平面上，距离相机相同

        适用条件：
            - 平面测量（所有物体距离相机相同）
            - 相机垂直于测量平面
            - 没有透视畸变
        """
        # 检查是否已标定
        if self.pixels_per_mm is None:
            raise ValueError("需要先进行标定，调用calibrate_with_reference_object()")

        # 执行转换计算
        physical_distance = pixel_distance / self.pixels_per_mm

        return physical_distance

    def pixel_to_mm_with_depth(self, pixel_distance, object_distance_mm):
        """
        考虑深度的像素到毫米转换（更精确的方法）

        参数详解:
            pixel_distance: 图像中的像素距离（同上）
                          - 单位：像素

            object_distance_mm: 被测物体到相机的距离
                              - 单位：毫米（mm）
                              - 含义：物体离相机镜头的实际距离
                              - 获取方法：激光测距仪、超声波传感器等
                              - 示例：400毫米

        数学原理详解:
            在不同距离处，相同物理尺寸对应的像素数不同

            基本原理：
            - 物体越远，在图像中越小（透视效应）
            - 需要根据距离调整像素密度

            计算步骤：
            1. scale_factor = object_distance_mm / reference_distance_mm
               - object_distance_mm: 当前物体距离（如400mm）
               - reference_distance_mm: 标定时的参考距离（如300mm）
               - scale_factor = 400/300 = 1.33（物体更远了）

            2. adjusted_pixels_per_mm = pixels_per_mm / scale_factor
               - pixels_per_mm: 标定时的像素密度（如5.0像素/mm）
               - adjusted_pixels_per_mm = 5.0/1.33 = 3.75像素/mm
               - 物理意义：距离更远时，每毫米对应的像素更少

            3. physical_distance = pixel_distance / adjusted_pixels_per_mm
               - 使用调整后的像素密度计算物理距离

        应用场景:
            - 物体距离相机的距离不同
            - 需要高精度测量
            - 有距离传感器可以测量深度
        """
        # 检查标定状态
        if self.pixels_per_mm is None or self.reference_distance is None:
            raise ValueError("需要先进行完整标定，调用calibrate_with_known_distance()")

        # 1. 计算距离缩放因子
        scale_factor = object_distance_mm / self.reference_distance

        # 2. 根据距离调整像素密度
        adjusted_pixels_per_mm = self.pixels_per_mm / scale_factor

        # 3. 计算物理距离
        physical_distance = pixel_distance / adjusted_pixels_per_mm

        # 输出调试信息
        print(f"距离校正：")
        print(f"  标定距离: {self.reference_distance}mm")
        print(f"  物体距离: {object_distance_mm}mm")
        print(f"  缩放因子: {scale_factor:.3f}")
        print(f"  调整后像素密度: {adjusted_pixels_per_mm:.3f} 像素/mm")

        return physical_distance

    def pixel_to_mm_perspective_correction(self, pixel_coords,
                                         camera_height_mm, camera_angle_deg=0):
        """
        透视校正的像素到毫米转换

        数学原理:
            考虑相机高度和角度的透视变换
            实际距离需要根据透视投影进行校正
        """
        if self.fx is None:
            raise ValueError("需要相机内参标定")

        u, v = pixel_coords

        # 转换为相机坐标系
        x_cam = (u - self.cx) / self.fx
        y_cam = (v - self.cy) / self.fy

        # 考虑相机角度
        angle_rad = math.radians(camera_angle_deg)

        # 透视校正计算
        # 假设物体在地面上（Z=0平面）
        if abs(y_cam * math.cos(angle_rad) + math.sin(angle_rad)) < 1e-6:
            return None  # 平行于地面，无法计算

        # 计算实际距离
        distance_to_object = camera_height_mm / (y_cam * math.cos(angle_rad) + math.sin(angle_rad))

        # 计算实际坐标
        real_x = x_cam * distance_to_object
        real_y = distance_to_object * math.cos(angle_rad) - camera_height_mm * math.sin(angle_rad)

        return real_x, real_y, distance_to_object

# 实际标定示例代码
def perform_calibration_example():
    """标定示例：使用标准物体进行标定"""

    converter = PixelToPhysicalConverter()

    # 方法1：使用已知尺寸的参考物体
    print("=== 方法1：参考物体标定 ===")

    # 假设有一个20mm的标准物体，在图像中占100像素
    reference_pixel_length = 100  # 像素
    reference_actual_length = 20  # 毫米

    pixels_per_mm = converter.calibrate_with_reference_object(
        reference_pixel_length, reference_actual_length
    )

    # 测试转换
    test_pixel_distance = 150
    physical_distance = converter.pixel_to_mm_simple(test_pixel_distance)
    print(f"150像素 = {physical_distance:.2f}mm")

    # 方法2：使用已知距离标定
    print("\n=== 方法2：距离标定 ===")

    converter2 = PixelToPhysicalConverter()

    # 假设相机距离物体300mm，视野宽度为100mm，图像宽度640像素
    fx, fy, cx, cy = converter2.calibrate_with_known_distance(
        image_width_pixels=640,
        actual_width_mm=100,
        distance_mm=300
    )

    # 测试不同距离的转换
    test_distances = [200, 300, 400]  # 不同距离（mm）
    for dist in test_distances:
        physical_dist = converter2.pixel_to_mm_with_depth(150, dist)
        print(f"在{dist}mm距离处，150像素 = {physical_dist:.2f}mm")

    return converter, converter2
```

#### 高度测量算法（含物理距离转换）

```python
def measure_shape_height_with_calibration(shape_info, converter, object_distance_mm=None):
    """
    测量形状高度并转换为物理单位

    参数:
        shape_info: 形状信息字典
        converter: 像素到物理距离转换器
        object_distance_mm: 物体距离（可选，用于深度校正）
    """

    # 1. 提取像素坐标
    top_point = shape_info['top_point']
    bottom_point = shape_info['bottom_point']

    # 2. 计算像素距离
    pixel_height = math.sqrt(
        (top_point[0] - bottom_point[0])**2 +
        (top_point[1] - bottom_point[1])**2
    )

    # 3. 转换为物理距离
    if object_distance_mm is not None:
        # 使用深度校正
        physical_height = converter.pixel_to_mm_with_depth(
            pixel_height, object_distance_mm
        )
    else:
        # 简单转换
        physical_height = converter.pixel_to_mm_simple(pixel_height)

    # 4. 计算测量精度
    def estimate_measurement_accuracy(pixel_height, pixels_per_mm):
        """
        估算测量精度
        假设像素定位精度为±0.5像素
        """
        pixel_uncertainty = 0.5  # 像素
        physical_uncertainty = pixel_uncertainty / pixels_per_mm
        relative_accuracy = physical_uncertainty / physical_height * 100

        return physical_uncertainty, relative_accuracy

    uncertainty, accuracy = estimate_measurement_accuracy(
        pixel_height, converter.pixels_per_mm
    )

    result = {
        'pixel_height': pixel_height,
        'physical_height_mm': physical_height,
        'measurement_uncertainty_mm': uncertainty,
        'relative_accuracy_percent': accuracy,
        'top_point_pixel': top_point,
        'bottom_point_pixel': bottom_point
    }

    return result

# 使用示例
def measurement_example():
    """测量示例"""

    # 初始化转换器并标定
    converter = PixelToPhysicalConverter()
    converter.calibrate_with_reference_object(pixel_length=100, actual_length_mm=20)

    # 模拟形状信息
    shape_info = {
        'name': 'Square',
        'top_point': (320, 100),
        'bottom_point': (320, 200),
        'center': (320, 150)
    }

    # 测量高度
    measurement = measure_shape_height_with_calibration(shape_info, converter)

    print(f"形状: {shape_info['name']}")
    print(f"像素高度: {measurement['pixel_height']:.1f} pixels")
    print(f"物理高度: {measurement['physical_height_mm']:.2f} mm")
    print(f"测量精度: ±{measurement['measurement_uncertainty_mm']:.2f} mm")
    print(f"相对精度: ±{measurement['relative_accuracy_percent']:.1f}%")

    return measurement
```

#### 多点标定提高精度

```python
def multi_point_calibration(reference_measurements):
    """
    使用多个参考点进行标定，提高精度

    参数:
        reference_measurements: [(pixel_length, actual_length_mm), ...]

    数学方法：最小二乘法拟合
    """

    if len(reference_measurements) < 2:
        raise ValueError("至少需要2个参考点")

    # 提取数据
    pixel_lengths = [m[0] for m in reference_measurements]
    actual_lengths = [m[1] for m in reference_measurements]

    # 最小二乘法拟合 y = kx (强制过原点)
    # k = Σ(xi*yi) / Σ(xi²)
    numerator = sum(p * a for p, a in zip(pixel_lengths, actual_lengths))
    denominator = sum(p * p for p in pixel_lengths)

    pixels_per_mm = denominator / numerator  # 注意：这里是倒数关系

    # 计算拟合质量（R²）
    def calculate_r_squared(pixel_lengths, actual_lengths, pixels_per_mm):
        predicted = [p / pixels_per_mm for p in pixel_lengths]
        actual_mean = sum(actual_lengths) / len(actual_lengths)

        ss_res = sum((actual - pred)**2 for actual, pred in zip(actual_lengths, predicted))
        ss_tot = sum((actual - actual_mean)**2 for actual in actual_lengths)

        r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
        return r_squared

    r_squared = calculate_r_squared(pixel_lengths, actual_lengths, pixels_per_mm)

    print(f"多点标定结果:")
    print(f"每毫米像素数: {pixels_per_mm:.4f}")
    print(f"拟合质量 R²: {r_squared:.4f}")

    return pixels_per_mm, r_squared
```

### 5. 完整标定流程和实际应用

#### 标定流程步骤详解

```python
class CompleteCalibratedShapeDetector(AdvancedShapeDetector):
    """
    集成标定功能的完整形状检测器
    """

    def __init__(self, width=640, height=480):
        super().__init__(width, height)
        self.converter = PixelToPhysicalConverter()
        self.is_calibrated = False
        self.calibration_mode = False

    def enter_calibration_mode(self):
        """进入标定模式"""
        self.calibration_mode = True
        print("进入标定模式...")
        print("请放置已知尺寸的参考物体")

    def calibrate_interactive(self):
        """交互式标定流程"""
        print("=== 交互式标定流程 ===")

        # 步骤1：选择标定方法
        print("选择标定方法:")
        print("1. 使用参考物体标定")
        print("2. 使用已知距离标定")
        print("3. 多点标定")

        method = input("请选择方法 (1-3): ")

        if method == "1":
            return self._calibrate_with_reference()
        elif method == "2":
            return self._calibrate_with_distance()
        elif method == "3":
            return self._calibrate_multi_point()
        else:
            print("无效选择")
            return False

    def _calibrate_with_reference(self):
        """参考物体标定"""
        print("\n=== 参考物体标定 ===")

        # 获取参考物体实际尺寸
        actual_length = float(input("请输入参考物体的实际长度(mm): "))

        print("请将参考物体放在视野中，按Enter开始检测...")
        input()

        # 检测参考物体
        frame = self.cam.read()
        if frame is None:
            print("无法获取图像")
            return False

        # 检测最大的矩形作为参考物体
        best_rect, x, y, w, h = self.detect_outer_rectangle_basic(frame)

        if best_rect is None:
            print("未检测到参考物体")
            return False

        # 计算参考物体的像素长度（取较长边）
        pixel_length = max(w, h)

        # 执行标定
        pixels_per_mm = self.converter.calibrate_with_reference_object(
            pixel_length, actual_length
        )

        self.is_calibrated = True
        self.calibration_mode = False

        print(f"标定成功！每毫米 {pixels_per_mm:.4f} 像素")
        return True

    def _calibrate_with_distance(self):
        """距离标定"""
        print("\n=== 距离标定 ===")

        # 获取标定参数
        actual_width = float(input("请输入视野实际宽度(mm): "))
        distance = float(input("请输入相机到物体的距离(mm): "))

        # 执行标定
        fx, fy, cx, cy = self.converter.calibrate_with_known_distance(
            self.width, actual_width, distance
        )

        self.is_calibrated = True
        self.calibration_mode = False

        print("距离标定完成！")
        return True

    def _calibrate_multi_point(self):
        """多点标定"""
        print("\n=== 多点标定 ===")

        measurements = []

        while True:
            print(f"\n当前已有 {len(measurements)} 个标定点")

            if len(measurements) >= 2:
                choice = input("添加更多标定点？(y/n): ")
                if choice.lower() != 'y':
                    break

            # 获取实际长度
            actual_length = float(input("请输入参考物体实际长度(mm): "))

            print("请将参考物体放在视野中，按Enter检测...")
            input()

            # 检测物体
            frame = self.cam.read()
            best_rect, x, y, w, h = self.detect_outer_rectangle_basic(frame)

            if best_rect is None:
                print("未检测到物体，跳过此点")
                continue

            pixel_length = max(w, h)
            measurements.append((pixel_length, actual_length))

            print(f"添加标定点: {pixel_length:.1f}像素 = {actual_length}mm")

        if len(measurements) < 2:
            print("标定点不足，标定失败")
            return False

        # 执行多点标定
        pixels_per_mm, r_squared = multi_point_calibration(measurements)
        self.converter.pixels_per_mm = pixels_per_mm

        self.is_calibrated = True
        self.calibration_mode = False

        print(f"多点标定完成！拟合质量: {r_squared:.4f}")
        return True

    def process_frame_with_calibration(self, frame):
        """带标定的帧处理"""
        if not self.is_calibrated:
            # 未标定时显示提示
            display_img = frame.copy()
            display_img.draw_string(10, 30, "Need Calibration",
                                  image.Color.from_rgb(255, 0, 0))
            return display_img

        # 执行正常检测
        display_img = self.process_frame(frame)

        # 添加物理尺寸信息
        if OPENCV_AVAILABLE:
            cv_img = self.maix_to_cv2(frame)
            if cv_img is not None:
                # 检测形状并添加物理尺寸
                self._add_physical_measurements(display_img, cv_img)

        return display_img

    def _add_physical_measurements(self, display_img, cv_img):
        """添加物理尺寸测量"""
        # 检测外边框
        if len(cv_img.shape) == 3:
            cv_gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
        else:
            cv_gray = cv_img

        best_rect, x, y, w, h = self.detect_outer_rectangle_opencv(cv_gray)

        if best_rect is not None:
            # 处理内部形状
            inner_x = x + int(w * 0.1)
            inner_y = y + int(h * 0.1)
            inner_w = int(w * 0.8)
            inner_h = int(h * 0.8)

            inner_roi = cv_gray[inner_y:inner_y+inner_h, inner_x:inner_x+inner_w]
            detected_shapes = self.detect_inner_shapes_opencv(inner_roi, inner_x, inner_y)

            # 为每个形状添加物理尺寸
            for shape_info in detected_shapes:
                measurement = measure_shape_height_with_calibration(
                    shape_info, self.converter
                )

                # 显示物理尺寸
                center_x, center_y = shape_info['center']
                physical_height = measurement['physical_height_mm']
                uncertainty = measurement['measurement_uncertainty_mm']

                # 显示测量结果
                text = f"{physical_height:.1f}±{uncertainty:.1f}mm"
                display_img.draw_string(center_x - 40, center_y + 20, text,
                                      image.Color.from_rgb(0, 255, 255))

    def save_calibration(self, filename="calibration.json"):
        """保存标定参数"""
        if not self.is_calibrated:
            print("未进行标定，无法保存")
            return False

        import json

        calibration_data = {
            'pixels_per_mm': self.converter.pixels_per_mm,
            'fx': self.converter.fx,
            'fy': self.converter.fy,
            'cx': self.converter.cx,
            'cy': self.converter.cy,
            'reference_distance': self.converter.reference_distance,
            'image_width': self.width,
            'image_height': self.height
        }

        try:
            with open(filename, 'w') as f:
                json.dump(calibration_data, f, indent=2)
            print(f"标定参数已保存到 {filename}")
            return True
        except Exception as e:
            print(f"保存失败: {e}")
            return False

    def load_calibration(self, filename="calibration.json"):
        """加载标定参数"""
        import json

        try:
            with open(filename, 'r') as f:
                calibration_data = json.load(f)

            self.converter.pixels_per_mm = calibration_data.get('pixels_per_mm')
            self.converter.fx = calibration_data.get('fx')
            self.converter.fy = calibration_data.get('fy')
            self.converter.cx = calibration_data.get('cx')
            self.converter.cy = calibration_data.get('cy')
            self.converter.reference_distance = calibration_data.get('reference_distance')

            self.is_calibrated = True
            print(f"标定参数已从 {filename} 加载")
            return True

        except Exception as e:
            print(f"加载失败: {e}")
            return False

# 完整使用示例
def complete_application_example():
    """完整应用示例"""
    print("=== MaixCAM形状检测完整应用示例 ===")

    # 1. 初始化检测器
    detector = CompleteCalibratedShapeDetector(width=640, height=480)

    # 2. 尝试加载已有标定
    if not detector.load_calibration():
        print("未找到标定文件，需要进行标定")

        # 3. 进行标定
        if detector.calibrate_interactive():
            # 4. 保存标定参数
            detector.save_calibration()
        else:
            print("标定失败，退出程序")
            return

    # 5. 开始检测
    print("开始形状检测和测量...")

    try:
        while not app.need_exit():
            frame = detector.cam.read()
            if frame is None:
                continue

            # 处理帧（包含物理尺寸测量）
            processed_frame = detector.process_frame_with_calibration(frame)
            detector.disp.show(processed_frame)

            detector.frame_count += 1

            # 性能监控
            if detector.frame_count % 60 == 0:
                current_time = time.ticks_ms()
                elapsed = current_time - detector.start_time
                fps = detector.frame_count * 1000 / elapsed if elapsed > 0 else 0
                print(f"FPS: {fps:.1f}, 已处理 {detector.frame_count} 帧")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("程序结束")

if __name__ == "__main__":
    complete_application_example()
```

#### 标定精度分析

**影响标定精度的因素：**

1. **像素定位精度**
   - 亚像素精度：±0.1像素
   - 普通精度：±0.5像素
   - 低精度：±1.0像素

2. **参考物体精度**
   - 测量工具精度：±0.1mm
   - 制造公差：±0.05mm
   - 温度影响：±0.01mm/°C

3. **光学畸变影响**
   - 桶形畸变：边缘区域误差增大
   - 枕形畸变：中心区域相对准确
   - 畸变校正：可提高精度至0.1%

**精度计算公式：**
```python
def calculate_measurement_precision():
    """计算测量精度"""

    # 系统参数
    pixel_uncertainty = 0.5  # 像素定位不确定度
    reference_uncertainty = 0.1  # 参考物体不确定度 (mm)
    pixels_per_mm = 5.0  # 标定结果

    # 标定不确定度传播
    calibration_uncertainty = math.sqrt(
        (reference_uncertainty / 20)**2 +  # 参考长度20mm
        (pixel_uncertainty / 100)**2       # 参考像素100px
    ) * pixels_per_mm

    # 测量不确定度
    def measurement_uncertainty(pixel_distance):
        pixel_error = pixel_uncertainty / pixels_per_mm
        calibration_error = pixel_distance * calibration_uncertainty / pixels_per_mm
        total_error = math.sqrt(pixel_error**2 + calibration_error**2)
        return total_error

    # 不同尺寸的测量精度
    test_sizes = [50, 100, 200, 500]  # 像素

    print("测量精度分析:")
    print("像素距离\t物理距离\t绝对精度\t相对精度")

    for pixels in test_sizes:
        physical = pixels / pixels_per_mm
        uncertainty = measurement_uncertainty(pixels)
        relative = uncertainty / physical * 100

        print(f"{pixels}px\t\t{physical:.1f}mm\t\t±{uncertainty:.2f}mm\t±{relative:.1f}%")

# 执行精度分析
calculate_measurement_precision()
```

### 🧮 完整计算示例（用具体数字演示）

**让我们用一个完整的例子来演示整个算法过程：**

#### 示例场景设置
```
场景：检测一个正方形物体的高度
- 相机分辨率：640×480像素
- 参考物体：20mm的硬币，在图像中占100像素
- 待测物体：未知尺寸的正方形
```

#### 步骤1：相机标定
```python
# 标定计算
pixel_length = 100        # 硬币在图像中的像素长度
actual_length_mm = 20     # 硬币的实际长度（毫米）

pixels_per_mm = pixel_length / actual_length_mm
pixels_per_mm = 100 / 20 = 5.0

结果：每毫米对应5像素
```

#### 步骤2：图像预处理（高斯模糊）
```python
# 对图像中某个像素点(100,100)进行高斯模糊
原始像素值 = [
    [120, 130, 140],    # 上一行
    [125, 135, 145],    # 当前行
    [130, 140, 150]     # 下一行
]

高斯核权重 = [
    [0.077, 0.123, 0.077],
    [0.123, 0.195, 0.123],
    [0.077, 0.123, 0.077]
]

# 卷积计算
新像素值 = 120×0.077 + 130×0.123 + 140×0.077 +
          125×0.123 + 135×0.195 + 145×0.123 +
          130×0.077 + 140×0.123 + 150×0.077
        = 9.24 + 15.99 + 10.78 + 15.38 + 26.33 + 17.84 + 10.01 + 17.22 + 11.55
        = 134.34

结果：像素值从135变为134.34（轻微模糊）
```

#### 步骤3：边缘检测（Sobel算子）
```python
# 对模糊后的图像计算梯度
模糊后像素值 = [
    [130, 134, 138],
    [132, 136, 140],
    [134, 138, 142]
]

Sobel_x = [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]
Sobel_y = [[-1, -2, -1], [0, 0, 0], [1, 2, 1]]

# x方向梯度计算
Gx = 130×(-1) + 134×0 + 138×1 +
     132×(-2) + 136×0 + 140×2 +
     134×(-1) + 138×0 + 142×1
   = -130 + 0 + 138 + (-264) + 0 + 280 + (-134) + 0 + 142
   = 32

# y方向梯度计算
Gy = 130×(-1) + 134×(-2) + 138×(-1) +
     132×0 + 136×0 + 140×0 +
     134×1 + 138×2 + 142×1
   = -130 + (-268) + (-138) + 0 + 0 + 0 + 134 + 276 + 142
   = 16

# 梯度幅值和方向
magnitude = √(Gx² + Gy²) = √(32² + 16²) = √(1024 + 256) = √1280 = 35.78
direction = arctan(Gy/Gx) × 180/π = arctan(16/32) × 180/π = 26.57°

结果：该点边缘强度为35.78，方向为26.57°
```

#### 步骤4：形状检测
```python
# 假设检测到一个正方形轮廓
正方形顶点 = [(200, 150), (300, 150), (300, 250), (200, 250)]

# 计算边长（像素）
边长1 = √((300-200)² + (150-150)²) = √(100² + 0²) = 100像素
边长2 = √((300-300)² + (250-150)²) = √(0² + 100²) = 100像素
边长3 = √((200-300)² + (250-250)²) = √(100² + 0²) = 100像素
边长4 = √((200-200)² + (150-250)²) = √(0² + 100²) = 100像素

# 宽高比检查
宽度 = 300 - 200 = 100像素
高度 = 250 - 150 = 100像素
宽高比 = 100/100 = 1.0

# 正方形判断
四边相等 = True（所有边都是100像素）
宽高比接近1 = True（1.0在0.9-1.1范围内）

结果：确认为正方形，边长100像素
```

#### 步骤5：物理尺寸转换
```python
# 简单转换（平面测量）
像素边长 = 100像素
pixels_per_mm = 5.0像素/毫米

物理边长 = 像素边长 / pixels_per_mm
         = 100 / 5.0
         = 20.0毫米

# 深度校正转换（如果物体距离不同）
假设：
- 标定距离 = 300mm
- 物体实际距离 = 400mm

缩放因子 = 400 / 300 = 1.33
调整后像素密度 = 5.0 / 1.33 = 3.76像素/毫米
校正后物理边长 = 100 / 3.76 = 26.6毫米

结果：
- 简单测量：20.0mm
- 深度校正：26.6mm（更准确）
```

#### 步骤6：精度分析
```python
# 测量不确定度计算
像素定位精度 = ±0.5像素
标定不确定度 = ±0.1毫米

# 像素转换误差
像素误差 = 0.5 / 5.0 = ±0.1毫米

# 标定传播误差
标定误差 = 20.0 × (0.1/20.0) = ±0.1毫米

# 总误差
总不确定度 = √(0.1² + 0.1²) = √0.02 = ±0.14毫米

# 相对精度
相对精度 = 0.14/20.0 × 100% = ±0.7%

最终结果：20.0 ± 0.14毫米（相对精度±0.7%）
```

### 📊 完整流程总结

```
输入：640×480图像 + 20mm参考硬币
↓
标定：每毫米5像素
↓
预处理：高斯模糊（σ=1.0）
↓
边缘检测：Sobel算子 → Canny边缘
↓
轮廓分析：找到正方形轮廓
↓
形状识别：100×100像素正方形
↓
距离转换：100像素 ÷ 5像素/mm = 20mm
↓
输出：20.0±0.14mm正方形（精度±0.7%）
```

## 技术规格和性能指标

### 系统要求
- **硬件平台**：MaixCAM / MaixCAM-Pro
- **操作系统**：MaixPy 4.0+
- **内存需求**：≥ 64MB RAM
- **存储需求**：≥ 16MB Flash

### 性能指标
| 指标 | 基础版本 | 高级版本 |
|------|---------|---------|
| 处理分辨率 | 640×480 | 640×480 |
| 帧率 | 15-20 FPS | 10-15 FPS |
| 检测精度 | 85% | 95% |
| 内存占用 | ~20MB | ~35MB |
| 启动时间 | <2秒 | <5秒 |

### 检测参数配置
```python
# 可调节参数
DETECTION_PARAMS = {
    'min_area': 2000,           # 外边框最小面积
    'min_inner_area': 300,      # 内部形状最小面积
    'gaussian_kernel': 5,       # 高斯核大小
    'canny_low': 50,           # Canny低阈值
    'canny_high': 150,         # Canny高阈值
    'aspect_ratio_min': 0.5,   # 最小宽高比
    'aspect_ratio_max': 2.0,   # 最大宽高比
    'circularity_threshold': 0.8,  # 圆形度阈值
    'triangle_ratio_threshold': 0.8  # 三角形边长比阈值
}
```

## 使用指南

### 快速开始
```bash
# 1. 确保MaixCAM环境
python -c "from maix import camera, display; print('环境OK')"

# 2. 运行程序
python maixcam_shape_detection_advanced.py

# 3. 观察输出
# - 绿色框：检测到外边框
# - 红色框：检测到内部形状
# - 黄色线：高度测量线
# - 青色点：测量端点
```

### 参数调优指南

#### 检测精度优化
```python
# 提高精度（降低性能）
detector.min_area = 1000          # 降低面积阈值
detector.canny_low = 30           # 降低Canny阈值
detector.gaussian_kernel = 3      # 减小模糊核

# 提高性能（降低精度）
detector.min_area = 3000          # 提高面积阈值
detector.canny_high = 200         # 提高Canny阈值
detector.gaussian_kernel = 7      # 增大模糊核
```

#### 环境适应性调整
```python
# 强光环境
detector.canny_low = 80
detector.canny_high = 200

# 弱光环境
detector.canny_low = 20
detector.canny_high = 100

# 高噪声环境
detector.gaussian_kernel = 7
detector.min_area = 3000
```

## 扩展开发

### 添加新形状类型
```python
def detect_pentagon(self, approx, area, perimeter):
    """检测五边形"""
    if len(approx) == 5:
        # 计算内角和验证
        angles = self.calculate_internal_angles(approx)
        expected_angle = 108  # 正五边形内角
        angle_variance = np.var(angles)

        if angle_variance < 100:  # 角度方差阈值
            return "Pentagon"
    return None
```

### 颜色检测集成
```python
def detect_shape_color(self, roi, shape_center):
    """检测形状颜色"""
    # 提取形状中心区域颜色
    center_x, center_y = shape_center
    color_roi = roi[center_y-5:center_y+5, center_x-5:center_x+5]

    # 计算平均颜色
    mean_color = np.mean(color_roi, axis=(0,1))

    # 颜色分类
    if mean_color[0] > 150 and mean_color[1] < 100:  # 红色
        return "Red"
    elif mean_color[1] > 150 and mean_color[0] < 100:  # 绿色
        return "Green"
    elif mean_color[2] > 150 and mean_color[0] < 100:  # 蓝色
        return "Blue"
    else:
        return "Unknown"
```

### 数据记录功能
```python
def save_detection_log(self, shapes, timestamp):
    """保存检测日志"""
    log_data = {
        'timestamp': timestamp,
        'frame_count': self.frame_count,
        'detected_shapes': []
    }

    for shape in shapes:
        shape_data = {
            'type': shape['name'],
            'height': shape['height'],
            'center': shape['center'],
            'area': cv2.contourArea(shape['contour'])
        }
        log_data['detected_shapes'].append(shape_data)

    # 保存到JSON文件
    with open(f'detection_log_{timestamp}.json', 'w') as f:
        json.dump(log_data, f, indent=2)
```

## 故障排除

### 常见问题及解决方案

#### 1. 检测不到外边框
**可能原因：**
- 光照条件不佳
- 背景对比度不足
- 面积阈值过高

**解决方案：**
```python
# 降低检测阈值
detector.min_area = 1000
detector.canny_low = 30

# 改善光照条件
# 调整摄像头曝光参数
detector.cam.exposure(1500)
```

#### 2. 形状识别错误
**可能原因：**
- 形状不够规整
- 噪声干扰
- 参数设置不当

**解决方案：**
```python
# 增加预处理
detector.gaussian_kernel = 7

# 调整形状判断阈值
circularity_threshold = 0.7  # 降低圆形度要求
aspect_ratio_tolerance = 0.2  # 增加宽高比容差
```

#### 3. 性能问题
**优化策略：**
```python
# 降低分辨率
detector = AdvancedShapeDetector(width=320, height=240)

# 减少处理频率
if self.frame_count % 2 == 0:  # 每隔一帧处理
    processed_frame = self.process_frame(frame)
```

## 版权声明

本项目由米醋电子工作室开发，基于MaixPy平台实现。代码遵循开源协议，可用于学习和商业用途。

**技术支持：**
- 项目地址：[GitHub链接]
- 技术文档：本文档
- 联系方式：[技术支持邮箱]

---

**文档版本：** v1.0
**最后更新：** 2024年
**作者：** Alex (Engineer) - 米醋电子工作室
