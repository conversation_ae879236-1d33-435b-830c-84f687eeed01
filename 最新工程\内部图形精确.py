import math
import numpy as np
from maix import camera, display, image, app, time

# 尝试导入OpenCV进行高级图像处理
try:
    import cv2
    OPENCV_AVAILABLE = True
    print("OpenCV可用，启用高级图像处理功能")
except ImportError:
    OPENCV_AVAILABLE = False
    print("OpenCV不可用，使用基础图像处理功能")

class AdvancedShapeDetector:
    """高级形状检测器类"""
    
    def __init__(self, width=640, height=480):
        """初始化检测器"""
        self.width = width
        self.height = height
        self.cam = camera.Camera(width, height)
        self.disp = display.Display()
        
        # 检测参数
        self.min_area = 2000
        self.min_inner_area = 300
        self.gaussian_kernel = 5
        self.canny_low = 50
        self.canny_high = 150
        
        # 相机参数 (需要根据实际相机校准)
        self.focal_length = 12.0  # 焦距12mm
        self.sensor_width = 7.62  # 传感器宽度mm (典型1/2.3英寸传感器)
        self.sensor_height = 5.44 # 传感器高度mm
        
        # A4纸尺寸 (mm)
        self.a4_width = 210.0
        self.a4_height = 297.0
        
        # 性能统计
        self.frame_count = 0
        self.start_time = time.ticks_ms()
        
        print("MaixCAM高级形状检测器初始化完成")
        print(f"分辨率: {width}x{height}")
        print(f"OpenCV支持: {'是' if OPENCV_AVAILABLE else '否'}")
        print(f"相机焦距: {self.focal_length}mm")
    
    def maix_to_cv2(self, maix_img):
        """将MaixPy图像转换为OpenCV格式"""
        if not OPENCV_AVAILABLE:
            return None

        try:
            # 获取图像数据
            img_data = maix_img.to_bytes()

            if maix_img.format() == image.Format.FMT_RGB888:
                # RGB格式转换
                np_array = np.frombuffer(img_data, dtype=np.uint8)
                np_array = np_array.reshape((maix_img.height(), maix_img.width(), 3))
                cv_img = cv2.cvtColor(np_array, cv2.COLOR_RGB2BGR)
            elif maix_img.format() == image.Format.FMT_GRAYSCALE:
                # 灰度格式转换
                np_array = np.frombuffer(img_data, dtype=np.uint8)
                cv_img = np_array.reshape((maix_img.height(), maix_img.width()))
            else:
                # 其他格式先转换为RGB
                rgb_img = maix_img.to_format(image.Format.FMT_RGB888)
                return self.maix_to_cv2(rgb_img)

            return cv_img
        except Exception as e:
            print(f"图像转换错误: {e}")
            return None
    
    def cv2_to_maix(self, cv_img):
        """将OpenCV图像转换为MaixPy格式"""
        if not OPENCV_AVAILABLE:
            return None
            
        if len(cv_img.shape) == 3:  # 彩色图像
            rgb_img = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
            return image.Image(cv_img.shape[1], cv_img.shape[0], 
                              image.Format.FMT_RGB888, rgb_img.tobytes())
        else:  # 灰度图像
            return image.Image(cv_img.shape[1], cv_img.shape[0], 
                              image.Format.FMT_GRAYSCALE, cv_img.tobytes())
    
    def calculate_distance(self, pixel_width, pixel_height):
        """
        根据检测到的A4纸像素尺寸计算距离
        公式: 距离 = (实际宽度 × 焦距 × 图像宽度) / (像素宽度 × 传感器宽度)
        """
        # 使用宽度和高度计算距离，然后取平均值
        distance_w = (self.a4_width * self.focal_length * self.width) / (pixel_width * self.sensor_width)
        distance_h = (self.a4_height * self.focal_length * self.height) / (pixel_height * self.sensor_height)
        
        return (distance_w + distance_h) / 2.0
    
    def calculate_real_size(self, pixel_size, distance):
        """
        根据距离计算实际尺寸
        公式: 实际尺寸 = (像素尺寸 × 传感器尺寸 × 距离) / (焦距 × 图像尺寸)
        """
        # 使用平均传感器尺寸和图像尺寸
        sensor_size = (self.sensor_width + self.sensor_height) / 2.0
        img_size = (self.width + self.height) / 2.0
        return (pixel_size * sensor_size * distance) / (self.focal_length * img_size)
    
    def detect_outer_rectangle_opencv(self, cv_gray):
        """使用OpenCV检测外边框"""
        try:
            # 高斯模糊减少噪声
            blurred = cv2.GaussianBlur(cv_gray, (self.gaussian_kernel, self.gaussian_kernel), 0)

            # 边缘检测
            edges = cv2.Canny(blurred, self.canny_low, self.canny_high)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            max_area = 0
            best_rect = None
            x, y, w, h = None, None, None, None

            for contour in contours:
                area = cv2.contourArea(contour)
                if area < self.min_area:
                    continue

                # 近似轮廓为多边形
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                # 检查是否为四边形
                if len(approx) == 4:
                    rect_x, rect_y, rect_w, rect_h = cv2.boundingRect(approx)
                    aspect_ratio = float(rect_w) / rect_h

                    # 过滤过于狭长的形状
                    if 0.5 < aspect_ratio < 2.0 and area > max_area:
                        max_area = area
                        best_rect = approx.reshape(-1, 2)  # 转换为(N,2)格式
                        x, y, w, h = rect_x, rect_y, rect_w, rect_h

            return best_rect, x, y, w, h
        except Exception as e:
            print(f"OpenCV外边框检测错误: {e}")
            return None, None, None, None, None
    
    def detect_inner_shapes_opencv(self, inner_roi, inner_x, inner_y, distance):
        """使用OpenCV检测内部形状"""
        detected_shapes = []
        
        if inner_roi.size == 0:
            return detected_shapes
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(inner_roi, (self.gaussian_kernel, self.gaussian_kernel), 0)
        
        # 边缘检测
        edges = cv2.Canny(blurred, self.canny_low, self.canny_high)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < self.min_inner_area:
                continue
            
            # 轮廓近似
            perimeter = cv2.arcLength(cnt, True)
            epsilon = 0.03 * perimeter
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            vertices = len(approx)
            
            # 计算形状中心
            M = cv2.moments(cnt)
            if M["m00"] == 0:
                continue
            
            cX = int(M["m10"] / M["m00"]) + inner_x
            cY = int(M["m01"] / M["m00"]) + inner_y
            
            # 形状判断
            shape = "none"
            shape_height_px = 0
            shape_width_px = 0
            real_height_mm = 0
            real_width_mm = 0
            top_point = (0, 0)
            bottom_point = (0, 0)
            left_point = (0, 0)
            right_point = (0, 0)
            
            if vertices == 3:  # 三角形
                # 检查是否为等边三角形
                side_lengths = []
                for i in range(3):
                    p1 = approx[i][0]
                    p2 = approx[(i+1)%3][0]
                    length = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                    side_lengths.append(length)
                
                max_side = max(side_lengths)
                ratios = [s/max_side for s in side_lengths]
                
                if all(r > 0.8 for r in ratios):
                    shape = "Triangle"
                    # 找到最高和最低点
                    points = approx.reshape(-1, 2)
                    top_idx = np.argmin(points[:, 1])
                    bottom_idx = np.argmax(points[:, 1])
                    left_idx = np.argmin(points[:, 0])
                    right_idx = np.argmax(points[:, 0])
                    
                    top_point = (points[top_idx][0] + inner_x, points[top_idx][1] + inner_y)
                    bottom_point = (points[bottom_idx][0] + inner_x, points[bottom_idx][1] + inner_y)
                    left_point = (points[left_idx][0] + inner_x, points[left_idx][1] + inner_y)
                    right_point = (points[right_idx][0] + inner_x, points[right_idx][1] + inner_y)
                    
                    shape_height_px = int(np.sqrt((top_point[0] - bottom_point[0])**2 + 
                                         (top_point[1] - bottom_point[1])**2))
                    shape_width_px = int(np.sqrt((left_point[0] - right_point[0])**2 + 
                                        (left_point[1] - right_point[1])**2))
                    
                    # 计算实际尺寸
                    real_height_mm = self.calculate_real_size(shape_height_px, distance)
                    real_width_mm = self.calculate_real_size(shape_width_px, distance)
            
            elif vertices == 4:  # 四边形
                x_cnt, y_cnt, w_cnt, h_cnt = cv2.boundingRect(approx)
                aspect = w_cnt / h_cnt
                if 0.9 < aspect < 1.1:
                    shape = "Square"
                    top_point = (cX, y_cnt + inner_y)
                    bottom_point = (cX, y_cnt + h_cnt + inner_y)
                    left_point = (x_cnt + inner_x, cY)
                    right_point = (x_cnt + w_cnt + inner_x, cY)
                    
                    shape_height_px = h_cnt
                    shape_width_px = w_cnt
                    
                    # 计算实际尺寸
                    real_height_mm = self.calculate_real_size(shape_height_px, distance)
                    real_width_mm = self.calculate_real_size(shape_width_px, distance)
            
            else:  # 可能是圆形
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                if circularity > 0.8:
                    shape = "Circle"
                    (circle_x, circle_y), radius = cv2.minEnclosingCircle(cnt)
                    circle_x = int(circle_x) + inner_x
                    circle_y = int(circle_y) + inner_y
                    radius = int(radius)
                    
                    top_point = (circle_x, circle_y - radius)
                    bottom_point = (circle_x, circle_y + radius)
                    left_point = (circle_x - radius, circle_y)
                    right_point = (circle_x + radius, circle_y)
                    
                    shape_height_px = radius * 2
                    shape_width_px = radius * 2
                    
                    # 计算实际尺寸
                    real_height_mm = self.calculate_real_size(shape_height_px, distance)
                    real_width_mm = self.calculate_real_size(shape_width_px, distance)
            
            # 保存形状信息
            if shape != "none":
                detected_shapes.append({
                    "name": shape,
                    "contour": cnt,
                    "height_px": shape_height_px,
                    "width_px": shape_width_px,
                    "height_mm": real_height_mm,
                    "width_mm": real_width_mm,
                    "top_point": top_point,
                    "bottom_point": bottom_point,
                    "left_point": left_point,
                    "right_point": right_point,
                    "center": (cX, cY)
                })
        
        return detected_shapes
    
    def detect_outer_rectangle_basic(self, maix_img):
        """使用基础方法检测外边框"""
        # 转换为灰度图
        if maix_img.format() != image.Format.FMT_GRAYSCALE:
            gray_img = maix_img.to_format(image.Format.FMT_GRAYSCALE)
        else:
            gray_img = maix_img
        
        # 使用色块检测
        thresholds = [[0, 100, -128, 127, -128, 127]]
        blobs = gray_img.find_blobs(thresholds, 
                                   pixels_threshold=self.min_area,
                                   area_threshold=self.min_area)
        
        max_area = 0
        best_rect = None
        x, y, w, h = None, None, None, None
        
        for blob in blobs:
            blob_area = blob[4]
            if blob_area > max_area:
                max_area = blob_area
                x, y, w, h = blob[0], blob[1], blob[2], blob[3]
                best_rect = [(x, y), (x + w, y), (x + w, y + h), (x, y + h)]
        
        return best_rect, x, y, w, h
    
    def process_frame(self, frame):
        """处理单帧图像"""
        try:
            display_img = frame.copy()

            distance = None
            outer_rect_info = None

            if OPENCV_AVAILABLE:
                # 使用OpenCV处理
                cv_img = self.maix_to_cv2(frame)
                if cv_img is None:
                    return display_img

                # 转换为灰度图
                if len(cv_img.shape) == 3:
                    cv_gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
                else:
                    cv_gray = cv_img

                # 检测外边框
                best_rect, x, y, w, h = self.detect_outer_rectangle_opencv(cv_gray)

                if best_rect is not None and w is not None and h is not None:
                    # 计算距离
                    distance = self.calculate_distance(w, h)
                    outer_rect_info = (best_rect, x, y, w, h)
            else:
                # 使用基础方法
                best_rect, x, y, w, h = self.detect_outer_rectangle_basic(frame)
                if best_rect is not None and w is not None and h is not None:
                    # 计算距离
                    distance = self.calculate_distance(w, h)
                    outer_rect_info = (best_rect, x, y, w, h)
        except Exception as e:
            print(f"帧处理错误: {e}")
            return frame

        try:
            # 显示状态
            status_text = "none"
            status_color = image.Color.from_rgb(255, 0, 0)

            if outer_rect_info is not None:
                best_rect, x, y, w, h = outer_rect_info
                status_text = "have"
                status_color = image.Color.from_rgb(0, 255, 0)

                # 绘制矩形
                for i in range(len(best_rect)):
                    p1 = best_rect[i]
                    p2 = best_rect[(i + 1) % len(best_rect)]
                    display_img.draw_line(int(p1[0]), int(p1[1]),
                                        int(p2[0]), int(p2[1]), status_color, 2)

                # 显示距离信息
                if distance is not None:
                    display_img.draw_string(x + w//2 - 50, y - 30,
                                          f"Dist: {distance:.1f}mm",
                                          image.Color.from_rgb(255, 255, 0))
        except Exception as e:
            print(f"绘制外边框错误: {e}")

            # 处理内部区域
            if OPENCV_AVAILABLE and x is not None and distance is not None:
                try:
                    # 内边框比外边框小2cm (根据距离计算像素偏移)
                    mm_per_pixel = (self.a4_width / w + self.a4_height / h) / 2.0
                    offset_px = int(20.0 / mm_per_pixel)  # 2cm = 20mm

                    inner_x = max(0, x + offset_px)
                    inner_y = max(0, y + offset_px)
                    inner_w = max(0, w - 2 * offset_px)
                    inner_h = max(0, h - 2 * offset_px)

                    # 确保内部区域在图像范围内
                    if inner_w > 0 and inner_h > 0:
                        # 提取内部区域
                        cv_img = self.maix_to_cv2(frame)
                        if cv_img is not None:
                            if len(cv_img.shape) == 3:
                                cv_gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
                            else:
                                cv_gray = cv_img

                            # 确保ROI在图像边界内
                            inner_y_end = min(inner_y + inner_h, cv_gray.shape[0])
                            inner_x_end = min(inner_x + inner_w, cv_gray.shape[1])

                            if inner_y < inner_y_end and inner_x < inner_x_end:
                                inner_roi = cv_gray[inner_y:inner_y_end, inner_x:inner_x_end]

                                # 检测内部形状
                                detected_shapes = self.detect_inner_shapes_opencv(inner_roi, inner_x, inner_y, distance)

                                # 绘制形状
                                try:
                                    for shape_info in detected_shapes:
                                        # 绘制高度线
                                        display_img.draw_line(shape_info["top_point"][0], shape_info["top_point"][1],
                                                            shape_info["bottom_point"][0], shape_info["bottom_point"][1],
                                                            image.Color.from_rgb(255, 255, 0), 2)

                                        # 绘制宽度线
                                        display_img.draw_line(shape_info["left_point"][0], shape_info["left_point"][1],
                                                            shape_info["right_point"][0], shape_info["right_point"][1],
                                                            image.Color.from_rgb(0, 255, 255), 2)

                                        # 显示形状名称和尺寸
                                        display_img.draw_string(shape_info["center"][0] - 30, shape_info["center"][1],
                                                              shape_info["name"], image.Color.from_rgb(255, 0, 0))

                                        # 显示高度和宽度
                                        size_text = f"H:{shape_info['height_mm']:.1f}mm W:{shape_info['width_mm']:.1f}mm"
                                        display_img.draw_string(shape_info["center"][0] - len(size_text)*4,
                                                              shape_info["center"][1] + 20,
                                                              size_text,
                                                              image.Color.from_rgb(255, 255, 0))

                                        # 标记端点
                                        display_img.draw_circle(shape_info["top_point"][0], shape_info["top_point"][1],
                                                              5, image.Color.from_rgb(0, 255, 255), -1)
                                        display_img.draw_circle(shape_info["bottom_point"][0], shape_info["bottom_point"][1],
                                                              5, image.Color.from_rgb(0, 255, 255), -1)
                                        display_img.draw_circle(shape_info["left_point"][0], shape_info["left_point"][1],
                                                              5, image.Color.from_rgb(255, 0, 255), -1)
                                        display_img.draw_circle(shape_info["right_point"][0], shape_info["right_point"][1],
                                                              5, image.Color.from_rgb(255, 0, 255), -1)
                                except Exception as e:
                                    print(f"绘制形状错误: {e}")
                except Exception as e:
                    print(f"内部区域处理错误: {e}")
        
        # 添加状态和性能信息
        display_img.draw_string(10, 30, status_text, status_color)
        
        if distance is not None:
            display_img.draw_string(10, 60, f"Distance: {distance:.1f}mm", 
                                  image.Color.from_rgb(255, 255, 255))
        
        # 计算FPS
        current_time = time.ticks_ms()
        elapsed = current_time - self.start_time
        if elapsed > 0:
            fps = self.frame_count * 1000 / elapsed
            display_img.draw_string(10, 90, f"FPS: {fps:.1f}", 
                                  image.Color.from_rgb(255, 255, 255))
        
        return display_img
    
    def run(self):
        """运行主循环"""
        print("开始高级形状检测...")
        print("程序正在运行，按Ctrl+C退出")

        try:
            while not app.need_exit():
                frame = self.cam.read()
                if frame is None:
                    continue

                processed_frame = self.process_frame(frame)
                self.disp.show(processed_frame)

                self.frame_count += 1

                # 每60帧输出一次性能信息
                if self.frame_count % 60 == 0:
                    current_time = time.ticks_ms()
                    elapsed = current_time - self.start_time
                    fps = self.frame_count * 1000 / elapsed if elapsed > 0 else 0
                    print(f"已处理 {self.frame_count} 帧, 平均FPS: {fps:.1f}")

                # 添加小延时避免CPU占用过高
                time.sleep_ms(1)

        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行出错: {e}")
        finally:
            print("程序结束")


def main():
    """主函数"""
    print("=" * 50)
    print("MaixCAM高级形状检测与尺寸测量程序")
    print("基于A4纸标定的距离和尺寸测量系统")
    print("版权所有：米醋电子工作室")
    print("=" * 50)
    print("使用说明:")
    print("1. 将A4纸放置在相机前方作为参考")
    print("2. 系统会自动检测A4纸并计算距离")
    print("3. 在A4纸内部2cm处检测内部形状并测量实际尺寸")
    print("4. 程序会实时显示检测结果")

    detector = AdvancedShapeDetector(width=640, height=480)
    detector.run()


if __name__ == "__main__":
    main()