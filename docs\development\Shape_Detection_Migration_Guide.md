# OpenCV到MaixCAM形状检测移植指南

## 概述

本文档详细说明了如何将OpenCV形状检测代码成功移植到MaixCAM平台，包含完整的代码实现和优化策略。

## 移植策略

### 1. 核心架构保持不变
- **算法逻辑**：保持原有的形状检测逻辑
- **功能模块**：外边框检测 + 内部形状识别 + 高度测量
- **检测类型**：三角形、正方形、圆形识别

### 2. 平台适配改造
- **显示接口**：cv2.imshow → maix.display.Display
- **图像格式**：OpenCV Mat → maix.image.Image
- **输入设备**：cv2.VideoCapture → maix.camera.Camera
- **绘制函数**：cv2绘制函数 → maix.image绘制方法

## 文件结构

```
├── maixcam_shape_detection.py          # 基础版本（纯MaixPy实现）
├── maixcam_shape_detection_advanced.py # 高级版本（OpenCV兼容）
└── docs/
    └── Shape_Detection_Migration_Guide.md  # 本文档
```

## 基础版本特性

### 文件：`maixcam_shape_detection.py`

**特点：**
- 纯MaixPy实现，无外部依赖
- 使用色块检测替代轮廓查找
- 适合资源受限环境
- 启动速度快

**核心功能：**
```python
class ShapeDetector:
    def detect_outer_rectangle(self, img):
        """使用色块检测寻找外边框"""
        thresholds = [[0, 100, -128, 127, -128, 127]]
        blobs = gray_img.find_blobs(thresholds, 
                                   pixels_threshold=self.min_area)
        # 返回最大面积的矩形
    
    def detect_inner_shapes(self, inner_roi, inner_x, inner_y):
        """基于宽高比判断形状类型"""
        # 正方形：0.9 < aspect_ratio < 1.1 且低填充率
        # 圆形：0.9 < aspect_ratio < 1.1 且高填充率  
        # 三角形：aspect_ratio偏离1.0较多
```

**使用方法：**
```bash
# 在MaixCAM上运行
python maixcam_shape_detection.py
```

## 高级版本特性

### 文件：`maixcam_shape_detection_advanced.py`

**特点：**
- OpenCV兼容层实现
- 完整的边缘检测和轮廓分析
- 更高的检测精度
- 自动降级到基础功能

**核心功能：**
```python
class AdvancedShapeDetector:
    def maix_to_cv2(self, maix_img):
        """MaixPy图像转OpenCV格式"""
        
    def detect_outer_rectangle_opencv(self, cv_gray):
        """完整的OpenCV边缘检测流程"""
        # 高斯模糊 → Canny边缘检测 → 轮廓查找 → 多边形近似
        
    def detect_inner_shapes_opencv(self, inner_roi, inner_x, inner_y):
        """精确的形状分类算法"""
        # 三角形：3个顶点 + 边长比例检查
        # 正方形：4个顶点 + 宽高比检查
        # 圆形：多顶点 + 圆形度计算
```

**使用方法：**
```bash
# 需要安装OpenCV（如果可用）
pip install opencv-python
python maixcam_shape_detection_advanced.py
```

## 关键技术对比

### 图像格式转换

**原始OpenCV：**
```python
gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
```

**MaixCAM基础版：**
```python
gray_img = img.to_format(image.Format.FMT_GRAYSCALE)
```

**MaixCAM高级版：**
```python
def maix_to_cv2(self, maix_img):
    img_data = maix_img.to_bytes()
    np_array = np.frombuffer(img_data, dtype=np.uint8)
    np_array = np_array.reshape((maix_img.height(), maix_img.width(), 3))
    cv_img = cv2.cvtColor(np_array, cv2.COLOR_RGB2BGR)
    return cv_img
```

### 边缘检测

**原始OpenCV：**
```python
edges = cv2.Canny(blurred, 50, 150)
contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
```

**MaixCAM基础版：**
```python
# 使用色块检测替代
thresholds = [[0, 100, -128, 127, -128, 127]]
blobs = gray_img.find_blobs(thresholds, pixels_threshold=min_area)
```

**MaixCAM高级版：**
```python
# 保持OpenCV原始实现
edges = cv2.Canny(blurred, self.canny_low, self.canny_high)
contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
```

### 显示和绘制

**原始OpenCV：**
```python
cv2.drawContours(display_img, [best_rect], -1, (0, 255, 0), 2)
cv2.putText(display_img, text, pos, cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
cv2.imshow("Shape Detection", processed_frame)
```

**MaixCAM版本：**
```python
# 绘制轮廓
for i in range(4):
    p1, p2 = best_rect[i], best_rect[(i + 1) % 4]
    display_img.draw_line(p1[0], p1[1], p2[0], p2[1], status_color, 2)

# 绘制文字
display_img.draw_string(10, 30, status_text, status_color)

# 显示图像
self.disp.show(processed_frame)
```

## 性能优化

### 1. 分辨率选择
```python
# 推荐配置
detector = ShapeDetector(width=640, height=480)  # 平衡性能和精度
# detector = ShapeDetector(width=320, height=240)  # 高性能模式
```

### 2. 参数调优
```python
# 检测参数
self.min_area = 2000          # 外边框最小面积
self.min_inner_area = 300     # 内部形状最小面积
self.gaussian_kernel = 5      # 高斯核大小
self.canny_low = 50          # Canny低阈值
self.canny_high = 150        # Canny高阈值
```

### 3. 性能监控
```python
# FPS计算
current_time = time.ticks_ms()
elapsed = current_time - self.start_time
fps = self.frame_count * 1000 / elapsed
```

## 使用建议

### 选择版本策略

1. **基础版本适用场景：**
   - MaixCAM资源受限
   - 不需要极高精度
   - 快速原型开发
   - 简单形状检测

2. **高级版本适用场景：**
   - 需要高精度检测
   - 复杂形状识别
   - 有OpenCV环境支持
   - 生产级应用

### 部署步骤

1. **准备MaixCAM环境**
```bash
# 确保MaixPy环境正常
python -c "from maix import camera, display, image; print('MaixPy OK')"
```

2. **选择合适版本**
```bash
# 测试OpenCV可用性
python -c "import cv2; print('OpenCV版本:', cv2.__version__)"
```

3. **运行程序**
```bash
# 基础版本
python maixcam_shape_detection.py

# 高级版本
python maixcam_shape_detection_advanced.py
```

### 调试技巧

1. **参数调整**
   - 根据实际场景调整阈值参数
   - 监控FPS确保性能满足需求
   - 观察检测结果调整面积过滤

2. **错误处理**
   - 检查摄像头初始化状态
   - 验证图像格式转换正确性
   - 监控内存使用情况

## 扩展功能

### 1. 添加新形状类型
```python
# 在detect_inner_shapes方法中添加
elif vertices == 5:  # 五边形
    shape = "Pentagon"
elif vertices == 6:  # 六边形  
    shape = "Hexagon"
```

### 2. 颜色检测
```python
# 结合色块检测
color_thresholds = {
    'red': [[0, 80, 40, 80, 10, 80]],
    'green': [[0, 80, -120, -10, 0, 30]],
    'blue': [[0, 80, 30, 100, -120, -60]]
}
```

### 3. 数据记录
```python
# 添加检测结果保存
def save_detection_result(self, shapes, timestamp):
    result = {
        'timestamp': timestamp,
        'shapes': shapes,
        'count': len(shapes)
    }
    # 保存到文件或数据库
```

## 总结

通过本移植指南，成功将OpenCV形状检测算法适配到MaixCAM平台，提供了两个版本：

- **基础版本**：纯MaixPy实现，适合资源受限场景
- **高级版本**：OpenCV兼容，提供更高精度

两个版本都保持了原始算法的核心功能，同时针对MaixCAM平台进行了优化，可以根据实际需求选择使用。
