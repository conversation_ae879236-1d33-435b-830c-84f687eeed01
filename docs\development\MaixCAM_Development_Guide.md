# MaixCAM开发完整指南

## 概述

本文档是基于MaixPy官方文档深度学习后整理的MaixCAM开发完整指南，涵盖基础图像处理和算法应用的核心技术栈。

## 1. 屏幕显示系统

### 1.1 基础显示操作
```python
from maix import display, image

# 创建显示对象
disp = display.Display()

# 创建图像并显示
img = image.Image(320, 240)
disp.show(img)
```

### 1.2 背光控制
```python
# 调整背光亮度 (0-100%)
disp.set_backlight(50)
```

### 1.3 支持的屏幕规格
- **2.3寸**: 552x368分辨率电容触摸屏 (MaixCAM默认)
- **2.4寸**: 640x480分辨率电容触摸屏 (MaixCAM-Pro)
- **5寸**: 854x480分辨率无触摸屏
- **7寸**: 1280x800分辨率电容触摸屏
- **LT9611**: MIPI转HDMI模块，支持多种分辨率

### 1.4 屏幕配置切换
修改`/boot/board`文件中的`pannel`键值：
- 2.3寸：`st7701_hd228001c31`
- 2.4寸：`st7701_lct024bsi20`
- 5寸：`st7701_dxq5d0019_V0`
- 7寸：`mtd700920b`

## 2. 摄像头系统

### 2.1 支持的摄像头类型
- **GC4653**: M12通用镜头，1/3"传感器，4M像素，适合AI识别
- **OS04A10**: M12通用镜头，1/1.8"大底传感器，4M像素，画质超清
- **OV2685**: 不支持镜头更换，1/5"传感器，2M像素
- **SC035HGS**: 黑白全局快门摄像头，30W像素，适合高速物体

### 2.2 基础摄像头操作
```python
from maix import camera, display, app

# 初始化摄像头和显示
cam = camera.Camera(640, 480)
disp = display.Display()

# 图像采集循环
while not app.need_exit():
    img = cam.read()
    disp.show(img)
```

### 2.3 分辨率和帧率配置
```python
# 不同帧率配置
cam = camera.Camera(640, 480, fps=30)   # 30fps
cam = camera.Camera(640, 480, fps=60)   # 60fps
cam = camera.Camera(640, 480, fps=80)   # 80fps

# 不同格式配置
cam = camera.Camera(640, 480, image.Format.FMT_GRAYSCALE)  # 灰度图
cam = camera.Camera(640, 480, image.Format.FMT_YVU420SP)   # NV21格式
```

### 2.4 摄像头参数调节
```python
# 曝光和增益控制
cam.exposure(1000)  # 设置曝光时间
cam.gain(100)       # 设置增益

# 白平衡控制
cam.awb_mode(camera.AwbMode.Manual)  # 手动白平衡
cam.set_wb_gain([0.134, 0.0625, 0.0625, 0.1239])  # 设置RGBB增益

# 图像质量调节
cam.luma(50)        # 亮度 (0-100)
cam.constrast(50)   # 对比度 (0-100)
cam.saturation(50)  # 饱和度 (0-100)
```

### 2.5 图像矫正
```python
# 镜头畸变矫正
img = cam.read()
img = img.lens_corr(strength=1.5)  # 调整strength值
```

## 3. 图像基础操作

### 3.1 图像创建和格式
```python
from maix import image

# 创建图像
img = image.Image(320, 240, image.Format.FMT_RGB888)

# 支持的格式
# FMT_RGB888, FMT_RGBA8888, FMT_GRAYSCALE, FMT_BGR888
```

### 3.2 图像文件操作
```python
# 加载图像
img = image.load("/root/image.jpg")

# 保存图像
img.save("/root/output.jpg")
```

### 3.3 图像绘制操作
```python
# 画矩形框
img.draw_rect(10, 10, 100, 100, image.Color.from_rgb(255, 0, 0))
img.draw_rect(10, 10, 100, 100, (255, 0, 0), thickness=-1)  # 实心框

# 画线
img.draw_line(10, 10, 100, 100, image.Color.from_rgb(255, 0, 0))

# 画圆
img.draw_circle(100, 100, 50, image.Color.from_rgb(255, 0, 0))

# 写文字
img.draw_string(10, 10, "Hello MaixPy", image.Color.from_rgb(255, 0, 0), scale=2)
```

### 3.4 中文字体支持
```python
# 加载中文字体
image.load_font("sourcehansans", "/maixapp/share/font/SourceHanSansCN-Regular.otf", size=32)
image.set_default_font("sourcehansans")

# 显示中文
img.draw_string(2, 2, "你好！Hello, world!", image.Color.from_rgba(255, 0, 0, 0.8))
```

### 3.5 图像变换操作
```python
# 缩放
img_new = img.resize(160, 120)

# 剪裁
img_new = img.crop(10, 10, 100, 100)

# 旋转
img_new = img.rotate(90)

# 拷贝
img_new = img.copy()

# 仿射变换
img_new = img.affine([(10, 10), (100, 10), (10, 100)], 
                     [(10, 10), (100, 20), (20, 100)])
```

## 4. 计算机视觉算法

### 4.1 色块检测
```python
from maix import image, camera, display

cam = camera.Camera(320, 240)
disp = display.Display()

# LAB颜色空间阈值 [L_MIN, L_MAX, A_MIN, A_MAX, B_MIN, B_MAX]
thresholds = [[0, 80, 40, 80, 10, 80]]      # 红色
# thresholds = [[0, 80, -120, -10, 0, 30]]    # 绿色
# thresholds = [[0, 80, 30, 100, -120, -60]]  # 蓝色

while 1:
    img = cam.read()
    blobs = img.find_blobs(thresholds, pixels_threshold=500)
    for blob in blobs:
        img.draw_rect(blob[0], blob[1], blob[2], blob[3], image.COLOR_GREEN)
    disp.show(img)
```

### 4.2 直线检测
```python
from maix import camera, display, image

cam = camera.Camera(320, 240)
disp = display.Display()

thresholds = [[0, 80, -120, -10, 0, 30]]  # 绿色线条

while 1:
    img = cam.read()
    lines = img.get_regression(thresholds, area_threshold=100)
    for line in lines:
        img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(), image.COLOR_GREEN, 2)
        
        # 计算角度
        theta = line.theta()
        if theta > 90:
            theta = 270 - theta
        else:
            theta = 90 - theta
        
        img.draw_string(0, 0, f"theta: {theta}, rho: {line.rho()}", image.COLOR_BLUE)
    disp.show(img)
```

### 4.3 二维码识别
```python
from maix import image, camera, display

cam = camera.Camera(320, 240)
disp = display.Display()

while 1:
    img = cam.read()
    qrcodes = img.find_qrcodes()
    for qr in qrcodes:
        # 画二维码边框
        corners = qr.corners()
        for i in range(4):
            img.draw_line(corners[i][0], corners[i][1], 
                         corners[(i + 1) % 4][0], corners[(i + 1) % 4][1], 
                         image.COLOR_RED)
        # 显示二维码内容
        img.draw_string(qr.x(), qr.y() - 15, qr.payload(), image.COLOR_RED)
    disp.show(img)
```

### 4.4 硬件加速二维码检测
```python
from maix import camera, display, app, image

cam = camera.Camera(320, 224)
disp = display.Display()
detector = image.QRCodeDetector()  # 硬件加速检测器

while not app.need_exit():
    img = cam.read()
    qrcodes = detector.detect(img)  # 可达60+fps
    for q in qrcodes:
        img.draw_string(0, 0, "payload: " + q.payload(), image.COLOR_BLUE)
    disp.show(img)
```

## 5. 性能优化技巧

### 5.1 分辨率选择策略
- **拍照/监控**: 使用高分辨率 (1920x1080, 1280x720)
- **AI识别**: 使用中等分辨率 (640x480, 320x240)
- **高速处理**: 使用低分辨率 (320x224, 224x224)

### 5.2 帧率优化
- 分辨率 ≤ 1280x720: 可设置60/80fps
- 分辨率 > 1280x720: 限制为30fps

### 5.3 算法优化
```python
# 使用灰度图提升处理速度
cam = camera.Camera(320, 240, image.Format.FMT_GRAYSCALE)

# 设置ROI区域减少计算量
blobs = img.find_blobs(thresholds, roi=[50, 50, 100, 100])

# 调整阈值过滤小目标
blobs = img.find_blobs(thresholds, area_threshold=1000, pixels_threshold=500)
```

## 6. 开发最佳实践

### 6.1 错误处理
```python
# 图像加载错误处理
img = image.load("/root/image.jpg")
if img is None:
    raise Exception("load image failed")

# 摄像头初始化
cam = camera.Camera(320, 240)
cam.skip_frames(30)  # 跳过初始不稳定帧
```

### 6.2 内存管理
```python
# 及时释放不需要的图像对象
img_new = img.resize(160, 120)
# 使用完img_new后，Python会自动回收内存
```

### 6.3 调试技巧
```python
# 显示到MaixVision进行调试
from maix import display
display.send_to_maixvision(img)

# 打印图像信息
print(f"Image: {img.width()}x{img.height()}, format: {img.format()}")
```

## 7. 常用参数参考

### 7.1 LAB颜色空间阈值
- **红色**: [0, 80, 40, 80, 10, 80]
- **绿色**: [0, 80, -120, -10, 0, 30]
- **蓝色**: [0, 80, 30, 100, -120, -60]

### 7.2 图像格式对照
- **FMT_RGB888**: RGB打包格式，最常用
- **FMT_GRAYSCALE**: 灰度图，处理速度快
- **FMT_BGR888**: BGR格式，OpenCV兼容
- **FMT_YVU420SP**: NV21格式，视频编码用

### 7.3 性能参考
- **色块检测**: 320x240@30fps
- **直线检测**: 320x240@30fps  
- **二维码识别**: 320x240@15fps
- **硬件加速二维码**: 320x224@60fps

## 8. OpenCV兼容性和转换

### 8.1 MaixPy与OpenCV的互操作
```python
import cv2
import numpy as np
from maix import image

# MaixPy Image转OpenCV
def maix_to_cv2(maix_img):
    # 转换为numpy数组
    np_array = np.frombuffer(maix_img.to_bytes(), dtype=np.uint8)
    if maix_img.format() == image.Format.FMT_RGB888:
        np_array = np_array.reshape((maix_img.height(), maix_img.width(), 3))
        cv_img = cv2.cvtColor(np_array, cv2.COLOR_RGB2BGR)
    elif maix_img.format() == image.Format.FMT_GRAYSCALE:
        cv_img = np_array.reshape((maix_img.height(), maix_img.width()))
    return cv_img

# OpenCV转MaixPy Image
def cv2_to_maix(cv_img):
    if len(cv_img.shape) == 3:  # 彩色图像
        rgb_img = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
        return image.Image(cv_img.shape[1], cv_img.shape[0],
                          image.Format.FMT_RGB888, rgb_img.tobytes())
    else:  # 灰度图像
        return image.Image(cv_img.shape[1], cv_img.shape[0],
                          image.Format.FMT_GRAYSCALE, cv_img.tobytes())
```

### 8.2 OpenCV算法移植策略
1. **保持算法逻辑不变**：核心检测算法保持一致
2. **替换显示接口**：用MaixPy的display替换cv2.imshow
3. **优化图像格式**：使用MaixPy原生格式提升性能
4. **适配硬件特性**：利用MaixCAM的硬件加速能力

## 总结

MaixCAM提供了完整的计算机视觉开发平台，通过MaixPy可以轻松实现：
1. 高效的图像采集和显示
2. 丰富的图像处理操作
3. 实用的计算机视觉算法
4. 灵活的硬件配置选项
5. 与OpenCV的良好兼容性

掌握这些核心技术后，可以开发出功能强大的视觉应用项目。
