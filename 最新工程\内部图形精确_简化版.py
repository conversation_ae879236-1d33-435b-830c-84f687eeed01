#!/usr/bin/env python3
"""
MaixCAM高级形状检测与尺寸测量程序 - 简化版
基于A4纸标定的距离和尺寸测量系统
版权所有：米醋电子工作室
"""

import math
import time
from maix import camera, display, image, app

class SimpleShapeDetector:
    """简化版形状检测器"""
    
    def __init__(self, width=640, height=480):
        """初始化检测器"""
        self.width = width
        self.height = height
        
        # 初始化摄像头和显示器
        try:
            self.cam = camera.Camera(width, height)
            self.disp = display.Display()
            print(f"摄像头和显示器初始化成功: {width}x{height}")
        except Exception as e:
            print(f"硬件初始化失败: {e}")
            raise
        
        # 检测参数
        self.min_area = 2000
        
        # A4纸尺寸 (mm)
        self.a4_width = 210.0
        self.a4_height = 297.0
        
        # 相机参数
        self.focal_length = 12.0  # 焦距12mm
        self.sensor_width = 7.62  # 传感器宽度mm
        self.sensor_height = 5.44 # 传感器高度mm
        
        # 性能统计
        self.frame_count = 0
        self.start_time = time.ticks_ms()
        
        print("MaixCAM简化版形状检测器初始化完成")
    
    def calculate_distance(self, pixel_width, pixel_height):
        """根据检测到的A4纸像素尺寸计算距离"""
        try:
            # 使用宽度和高度计算距离，然后取平均值
            distance_w = (self.a4_width * self.focal_length * self.width) / (pixel_width * self.sensor_width)
            distance_h = (self.a4_height * self.focal_length * self.height) / (pixel_height * self.sensor_height)
            return (distance_w + distance_h) / 2.0
        except Exception as e:
            print(f"距离计算错误: {e}")
            return None
    
    def calculate_real_size(self, pixel_size, distance):
        """根据距离计算实际尺寸"""
        try:
            sensor_size = (self.sensor_width + self.sensor_height) / 2.0
            img_size = (self.width + self.height) / 2.0
            return (pixel_size * sensor_size * distance) / (self.focal_length * img_size)
        except Exception as e:
            print(f"尺寸计算错误: {e}")
            return 0
    
    def detect_outer_rectangle(self, frame):
        """检测外边框 - 使用基础方法"""
        try:
            # 转换为灰度图
            if frame.format() != image.Format.FMT_GRAYSCALE:
                gray_img = frame.to_format(image.Format.FMT_GRAYSCALE)
            else:
                gray_img = frame
            
            # 使用色块检测寻找最大的矩形区域
            thresholds = [[0, 100, -128, 127, -128, 127]]
            blobs = gray_img.find_blobs(thresholds, 
                                       pixels_threshold=self.min_area,
                                       area_threshold=self.min_area)
            
            max_area = 0
            best_blob = None
            
            for blob in blobs:
                blob_area = blob[4]  # 面积
                if blob_area > max_area:
                    max_area = blob_area
                    best_blob = blob
            
            if best_blob is not None:
                x, y, w, h = best_blob[0], best_blob[1], best_blob[2], best_blob[3]
                # 创建矩形顶点
                rect_points = [(x, y), (x + w, y), (x + w, y + h), (x, y + h)]
                return rect_points, x, y, w, h
            
            return None, None, None, None, None
            
        except Exception as e:
            print(f"外边框检测错误: {e}")
            return None, None, None, None, None
    
    def detect_inner_shapes(self, frame, outer_x, outer_y, outer_w, outer_h, distance):
        """检测内部形状 - 简化版"""
        detected_shapes = []
        
        try:
            # 计算内部区域 (比外边框小2cm)
            mm_per_pixel = (self.a4_width / outer_w + self.a4_height / outer_h) / 2.0
            offset_px = max(10, int(20.0 / mm_per_pixel))  # 2cm = 20mm，最小10像素
            
            inner_x = outer_x + offset_px
            inner_y = outer_y + offset_px
            inner_w = outer_w - 2 * offset_px
            inner_h = outer_h - 2 * offset_px
            
            # 确保内部区域有效
            if inner_w <= 0 or inner_h <= 0:
                return detected_shapes
            
            # 提取内部区域
            inner_roi = frame.crop(inner_x, inner_y, inner_w, inner_h)
            if inner_roi.format() != image.Format.FMT_GRAYSCALE:
                inner_roi = inner_roi.to_format(image.Format.FMT_GRAYSCALE)
            
            # 在内部区域寻找形状
            thresholds = [[0, 80, -128, 127, -128, 127]]
            blobs = inner_roi.find_blobs(thresholds, 
                                        pixels_threshold=300,
                                        area_threshold=300)
            
            for blob in blobs:
                blob_x, blob_y, blob_w, blob_h = blob[0], blob[1], blob[2], blob[3]
                blob_area = blob[4]
                
                # 计算形状中心 (相对于原图)
                center_x = inner_x + blob_x + blob_w // 2
                center_y = inner_y + blob_y + blob_h // 2
                
                # 简单形状判断
                aspect_ratio = blob_w / blob_h if blob_h > 0 else 1
                
                shape_name = "Unknown"
                if 0.8 < aspect_ratio < 1.2:
                    shape_name = "Square"
                elif aspect_ratio > 1.5:
                    shape_name = "Rectangle"
                else:
                    shape_name = "Shape"
                
                # 计算实际尺寸
                real_width = self.calculate_real_size(blob_w, distance)
                real_height = self.calculate_real_size(blob_h, distance)
                
                # 计算关键点
                top_point = (center_x, inner_y + blob_y)
                bottom_point = (center_x, inner_y + blob_y + blob_h)
                left_point = (inner_x + blob_x, center_y)
                right_point = (inner_x + blob_x + blob_w, center_y)
                
                detected_shapes.append({
                    "name": shape_name,
                    "center": (center_x, center_y),
                    "width_mm": real_width,
                    "height_mm": real_height,
                    "width_px": blob_w,
                    "height_px": blob_h,
                    "top_point": top_point,
                    "bottom_point": bottom_point,
                    "left_point": left_point,
                    "right_point": right_point,
                    "bbox": (inner_x + blob_x, inner_y + blob_y, blob_w, blob_h)
                })
            
        except Exception as e:
            print(f"内部形状检测错误: {e}")
        
        return detected_shapes
    
    def process_frame(self, frame):
        """处理单帧图像"""
        try:
            display_img = frame.copy()
            
            # 检测外边框
            rect_points, x, y, w, h = self.detect_outer_rectangle(frame)
            
            status_text = "No A4 Paper"
            status_color = image.Color.from_rgb(255, 0, 0)
            distance = None
            
            if rect_points is not None and w is not None and h is not None:
                status_text = "A4 Paper Found"
                status_color = image.Color.from_rgb(0, 255, 0)
                
                # 绘制外边框
                for i in range(len(rect_points)):
                    p1 = rect_points[i]
                    p2 = rect_points[(i + 1) % len(rect_points)]
                    display_img.draw_line(int(p1[0]), int(p1[1]), 
                                        int(p2[0]), int(p2[1]), status_color, 2)
                
                # 计算距离
                distance = self.calculate_distance(w, h)
                if distance is not None:
                    display_img.draw_string(x + w//2 - 50, y - 30, 
                                          f"Dist: {distance:.1f}mm", 
                                          image.Color.from_rgb(255, 255, 0))
                
                # 检测内部形状
                detected_shapes = self.detect_inner_shapes(frame, x, y, w, h, distance)
                
                # 绘制内部形状
                for shape_info in detected_shapes:
                    # 绘制形状边框
                    bbox = shape_info["bbox"]
                    display_img.draw_rect(bbox[0], bbox[1], bbox[2], bbox[3], 
                                        image.Color.from_rgb(0, 255, 255), 2)
                    
                    # 显示形状信息
                    center = shape_info["center"]
                    display_img.draw_string(center[0] - 30, center[1] - 10,
                                          shape_info["name"], 
                                          image.Color.from_rgb(255, 0, 0))
                    
                    # 显示尺寸
                    size_text = f"{shape_info['width_mm']:.1f}x{shape_info['height_mm']:.1f}mm"
                    display_img.draw_string(center[0] - 40, center[1] + 10,
                                          size_text, 
                                          image.Color.from_rgb(255, 255, 0))
                    
                    # 标记中心点
                    display_img.draw_circle(center[0], center[1], 3, 
                                          image.Color.from_rgb(255, 0, 255), -1)
            
            # 显示状态信息
            display_img.draw_string(10, 30, status_text, status_color)
            
            if distance is not None:
                display_img.draw_string(10, 60, f"Distance: {distance:.1f}mm", 
                                      image.Color.from_rgb(255, 255, 255))
            
            # 显示FPS
            current_time = time.ticks_ms()
            elapsed = current_time - self.start_time
            if elapsed > 0:
                fps = self.frame_count * 1000 / elapsed
                display_img.draw_string(10, 90, f"FPS: {fps:.1f}", 
                                      image.Color.from_rgb(255, 255, 255))
            
            return display_img
            
        except Exception as e:
            print(f"帧处理错误: {e}")
            return frame
    
    def run(self):
        """运行主循环"""
        print("开始简化版形状检测...")
        print("程序正在运行，按Ctrl+C退出")
        
        try:
            while not app.need_exit():
                frame = self.cam.read()
                if frame is None:
                    continue
                
                processed_frame = self.process_frame(frame)
                self.disp.show(processed_frame)
                
                self.frame_count += 1
                
                # 每60帧输出一次性能信息
                if self.frame_count % 60 == 0:
                    current_time = time.ticks_ms()
                    elapsed = current_time - self.start_time
                    fps = self.frame_count * 1000 / elapsed if elapsed > 0 else 0
                    print(f"已处理 {self.frame_count} 帧, 平均FPS: {fps:.1f}")
                
                # 添加小延时避免CPU占用过高
                time.sleep_ms(1)
                
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行出错: {e}")
        finally:
            print("程序结束")


def main():
    """主函数"""
    print("=" * 50)
    print("MaixCAM高级形状检测与尺寸测量程序 - 简化版")
    print("基于A4纸标定的距离和尺寸测量系统")
    print("版权所有：米醋电子工作室")
    print("=" * 50)
    print("使用说明:")
    print("1. 将A4纸放置在相机前方作为参考")
    print("2. 系统会自动检测A4纸并计算距离")
    print("3. 在A4纸内部检测形状并测量实际尺寸")
    print("4. 程序会实时显示检测结果")
    
    try:
        detector = SimpleShapeDetector(width=640, height=480)
        detector.run()
    except Exception as e:
        print(f"程序启动失败: {e}")


if __name__ == "__main__":
    main()
