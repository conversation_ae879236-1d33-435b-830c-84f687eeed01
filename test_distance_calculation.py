#!/usr/bin/env python3
"""
测试摄像头到目标物距离计算功能
验证距离计算算法的准确性
"""

import sys
import math

def test_code_syntax():
    """测试代码语法是否正确"""
    print("🔍 测试代码语法...")
    
    try:
        with open('maixcam_shape_detection.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, 'maixcam_shape_detection.py', 'exec')
        print("✅ 代码语法检查通过")
        return True
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"⚠️  其他错误: {e}")
        return False

def check_distance_functions():
    """检查距离计算函数是否正确添加"""
    print("\n🔍 检查距离计算函数...")
    
    with open('maixcam_shape_detection.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    # 检查基础距离计算函数
    if 'def calculate_object_distance(' in code:
        print("✅ 基础距离计算函数已添加")
    else:
        print("❌ 基础距离计算函数缺失")
        return False
    
    # 检查高级距离计算函数
    if 'def calculate_object_distance_advanced(' in code:
        print("✅ 高级距离计算函数已添加")
    else:
        print("❌ 高级距离计算函数缺失")
        return False
    
    # 检查距离显示
    if 'Distance:' in code:
        print("✅ 距离显示功能已添加")
    else:
        print("❌ 距离显示功能缺失")
        return False
    
    return True

def check_calibration_parameters():
    """检查标定参数"""
    print("\n🔍 检查标定参数...")
    
    with open('maixcam_shape_detection.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    # 检查焦距参数
    if 'FOCAL_LENGTH' in code:
        print("✅ 焦距参数已定义")
    else:
        print("❌ 焦距参数缺失")
        return False
    
    # 检查标准长度参数
    if 'LEN_BOX = 21.0' in code:
        print("✅ 标准长度参数正确")
    else:
        print("❌ 标准长度参数有问题")
        return False
    
    return True

def simulate_distance_calculation():
    """模拟距离计算验证"""
    print("\n🧮 模拟距离计算验证...")
    
    # 模拟参数
    LEN_BOX = 21.0  # 标准物体实际长度 (mm)
    FOCAL_LENGTH = 500.0  # 摄像头焦距 (像素)
    
    # 测试场景1: 近距离
    print("\n📐 测试场景1: 近距离物体")
    object_width_px = 100  # 物体宽度(像素)
    object_height_px = 80   # 物体高度(像素)
    
    # 计算比例尺
    Height_Box = min(object_height_px, object_width_px)  # 80
    Scale = LEN_BOX / Height_Box  # 21.0 / 80 = 0.2625 mm/px
    
    # 基础距离计算
    reference_size_px = min(object_width_px, object_height_px)  # 80
    distance_basic = (LEN_BOX * FOCAL_LENGTH) / reference_size_px
    # distance = (21.0 * 500) / 80 = 131.25 mm
    
    # 高级距离计算
    actual_width = object_width_px * Scale  # 100 * 0.2625 = 26.25
    actual_height = object_height_px * Scale  # 80 * 0.2625 = 21.0
    avg_size_px = (object_width_px + object_height_px) / 2  # 90
    avg_size_mm = (actual_width + actual_height) / 2  # 23.625
    distance_advanced = (avg_size_mm * FOCAL_LENGTH) / avg_size_px
    # distance = (23.625 * 500) / 90 = 131.25 mm
    
    print(f"   物体像素尺寸: {object_width_px}×{object_height_px} px")
    print(f"   比例尺: {Scale:.4f} mm/px")
    print(f"   基础距离计算: {distance_basic:.1f} mm")
    print(f"   高级距离计算: {distance_advanced:.1f} mm")
    
    # 测试场景2: 远距离
    print("\n📐 测试场景2: 远距离物体")
    object_width_px = 40   # 物体宽度(像素)
    object_height_px = 32  # 物体高度(像素)
    
    Height_Box = min(object_height_px, object_width_px)  # 32
    Scale = LEN_BOX / Height_Box  # 21.0 / 32 = 0.65625 mm/px
    
    reference_size_px = min(object_width_px, object_height_px)  # 32
    distance_basic = (LEN_BOX * FOCAL_LENGTH) / reference_size_px
    # distance = (21.0 * 500) / 32 = 328.125 mm
    
    actual_width = object_width_px * Scale  # 40 * 0.65625 = 26.25
    actual_height = object_height_px * Scale  # 32 * 0.65625 = 21.0
    avg_size_px = (object_width_px + object_height_px) / 2  # 36
    avg_size_mm = (actual_width + actual_height) / 2  # 23.625
    distance_advanced = (avg_size_mm * FOCAL_LENGTH) / avg_size_px
    # distance = (23.625 * 500) / 36 = 328.125 mm
    
    print(f"   物体像素尺寸: {object_width_px}×{object_height_px} px")
    print(f"   比例尺: {Scale:.4f} mm/px")
    print(f"   基础距离计算: {distance_basic:.1f} mm")
    print(f"   高级距离计算: {distance_advanced:.1f} mm")
    
    print("\n✅ 距离计算逻辑验证通过")
    return True

def explain_distance_algorithm():
    """解释距离计算算法原理"""
    print("\n📚 距离计算算法原理:")
    print("=" * 50)
    print("🔬 基于相似三角形原理:")
    print("   距离 = (实际物体尺寸 × 摄像头焦距) / 像素尺寸")
    print()
    print("📐 计算步骤:")
    print("   1. 获取物体在图像中的像素尺寸")
    print("   2. 通过比例尺计算物体实际尺寸")
    print("   3. 使用相似三角形公式计算距离")
    print()
    print("🎯 两种计算方法:")
    print("   • 基础方法：使用最小边作为参考")
    print("   • 高级方法：使用平均尺寸提高稳定性")
    print()
    print("⚙️  关键参数:")
    print("   • LEN_BOX: 标准物体实际长度 (21.0mm)")
    print("   • FOCAL_LENGTH: 摄像头焦距 (500像素)")
    print("   • Scale: 比例尺 (mm/pixel)")
    print("=" * 50)

def show_usage_guide():
    """显示使用指南"""
    print("\n📋 使用指南:")
    print("=" * 50)
    print("🎮 运行程序:")
    print("   python maixcam_shape_detection.py")
    print()
    print("📊 界面显示信息:")
    print("   • 左上角: 检测状态 (have/none)")
    print("   • 第二行: 比例尺 (Scale: X.XXXmm/px)")
    print("   • 第三行: 焦距 (Focal: 500px)")
    print("   • 外边框右侧: 宽高 (像素+毫米)")
    print("   • 外边框下方: 距离 (Distance: XXX.Xmm)")
    print()
    print("🔧 标定建议:")
    print("   • 确保标准物体最小边确实是21.0mm")
    print("   • 根据实际摄像头调整FOCAL_LENGTH参数")
    print("   • 保持稳定的光照条件")
    print("=" * 50)

if __name__ == "__main__":
    print("🚀 MaixCAM距离计算功能测试")
    print("=" * 50)
    
    all_passed = True
    
    # 语法检查
    if not test_code_syntax():
        all_passed = False
    
    # 函数检查
    if not check_distance_functions():
        all_passed = False
    
    # 参数检查
    if not check_calibration_parameters():
        all_passed = False
    
    # 计算验证
    if not simulate_distance_calculation():
        all_passed = False
    
    # 算法解释
    explain_distance_algorithm()
    
    # 使用指南
    show_usage_guide()
    
    if all_passed:
        print("\n🎉 所有检查通过！距离计算功能已成功添加！")
        print("💡 提示：在MaixCAM设备上运行即可看到实时距离测量效果")
    else:
        print("\n❌ 部分检查未通过，请检查代码")
        sys.exit(1)
