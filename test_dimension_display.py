#!/usr/bin/env python3
"""
测试外边框长宽显示功能
验证修改后的代码是否正确显示尺寸信息
"""

import sys
import os

def test_code_syntax():
    """测试代码语法是否正确"""
    print("🔍 测试代码语法...")
    
    # 测试基础版本
    try:
        with open('maixcam_shape_detection.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, 'maixcam_shape_detection.py', 'exec')
        print("✅ maixcam_shape_detection.py 语法检查通过")
    except SyntaxError as e:
        print(f"❌ maixcam_shape_detection.py 语法错误: {e}")
        return False
    except Exception as e:
        print(f"⚠️  maixcam_shape_detection.py 其他错误: {e}")
    
    # 测试高级版本
    try:
        with open('maixcam_shape_detection_advanced.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, 'maixcam_shape_detection_advanced.py', 'exec')
        print("✅ maixcam_shape_detection_advanced.py 语法检查通过")
    except SyntaxError as e:
        print(f"❌ maixcam_shape_detection_advanced.py 语法错误: {e}")
        return False
    except Exception as e:
        print(f"⚠️  maixcam_shape_detection_advanced.py 其他错误: {e}")
    
    return True

def check_dimension_display_code():
    """检查长宽显示代码是否正确添加"""
    print("\n🔍 检查长宽显示功能...")

    # 检查基础版本
    with open('maixcam_shape_detection.py', 'r', encoding='utf-8') as f:
        basic_code = f.read()

    if 'W:{w}px' in basic_code and 'H:{h}px' in basic_code:
        print("✅ 基础版本：长宽显示代码已正确添加")
    else:
        print("❌ 基础版本：长宽显示代码缺失")
        return False

    # 检查变量安全性
    if 'x is not None and y is not None and w is not None and h is not None' in basic_code:
        print("✅ 基础版本：变量安全检查已添加")
    else:
        print("❌ 基础版本：缺少变量安全检查")
        return False

    # 检查高级版本
    with open('maixcam_shape_detection_advanced.py', 'r', encoding='utf-8') as f:
        advanced_code = f.read()

    if 'W:{w}px' in advanced_code and 'H:{h}px' in advanced_code:
        print("✅ 高级版本：长宽显示代码已正确添加")
    else:
        print("❌ 高级版本：长宽显示代码缺失")
        return False

    # 检查变量安全性
    if 'x is not None and y is not None and w is not None and h is not None' in advanced_code:
        print("✅ 高级版本：变量安全检查已添加")
    else:
        print("❌ 高级版本：缺少变量安全检查")
        return False

    return True

def show_modification_summary():
    """显示修改摘要"""
    print("\n📋 修改摘要:")
    print("=" * 50)
    print("🎯 功能：在外层绿色框旁边显示长宽数值")
    print("📁 修改文件：")
    print("   - maixcam_shape_detection.py (基础版本)")
    print("   - maixcam_shape_detection_advanced.py (高级版本)")
    print("\n🔧 添加的功能：")
    print("   - 宽度显示：在外边框右上角显示 'W:XXXpx'")
    print("   - 高度显示：在外边框右侧中间显示 'H:XXXpx'")
    print("   - 颜色：绿色文字，与外边框颜色一致")
    print("   - 位置：框外侧，不遮挡检测内容")
    print("   - 安全检查：防止变量为None时出错")
    print("\n💡 使用方法：")
    print("   python maixcam_shape_detection.py          # 基础版本")
    print("   python maixcam_shape_detection_advanced.py # 高级版本")
    print("=" * 50)

if __name__ == "__main__":
    print("🚀 MaixCAM形状检测 - 长宽显示功能测试")
    print("=" * 50)
    
    # 测试语法
    if not test_code_syntax():
        print("\n❌ 测试失败：代码语法错误")
        sys.exit(1)
    
    # 检查功能
    if not check_dimension_display_code():
        print("\n❌ 测试失败：长宽显示功能未正确添加")
        sys.exit(1)
    
    # 显示摘要
    show_modification_summary()
    
    print("\n🎉 测试完成：所有检查通过！")
    print("💡 提示：在MaixCAM设备上运行代码即可看到长宽显示效果")
