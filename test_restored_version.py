#!/usr/bin/env python3
"""
测试恢复后的MaixCAM形状检测代码
验证是否恢复到稳定的工作版本
"""

import sys

def test_code_syntax():
    """测试代码语法是否正确"""
    print("🔍 测试代码语法...")
    
    try:
        with open('maixcam_shape_detection.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, 'maixcam_shape_detection.py', 'exec')
        print("✅ 代码语法检查通过")
        return True
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"⚠️  其他错误: {e}")
        return False

def check_removed_features():
    """检查是否已移除有问题的功能"""
    print("\n🔍 检查已移除的功能...")
    
    with open('maixcam_shape_detection.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    removed_count = 0
    
    # 检查距离计算函数是否已移除
    if 'def calculate_object_distance(' not in code:
        print("✅ 距离计算函数已移除")
        removed_count += 1
    else:
        print("❌ 距离计算函数仍存在")
    
    # 检查焦距参数是否已移除
    if 'FOCAL_LENGTH' not in code:
        print("✅ 焦距参数已移除")
        removed_count += 1
    else:
        print("❌ 焦距参数仍存在")
    
    # 检查距离显示是否已移除
    if 'Distance:' not in code:
        print("✅ 距离显示已移除")
        removed_count += 1
    else:
        print("❌ 距离显示仍存在")
    
    print(f"📊 移除功能完成度: {removed_count}/3")
    return removed_count >= 2

def check_core_features():
    """检查核心功能是否保留"""
    print("\n🔍 检查核心功能...")
    
    with open('maixcam_shape_detection.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    core_count = 0
    
    # 检查比例尺功能
    if 'LEN_BOX = 21.0' in code and 'Scale =' in code:
        print("✅ 比例尺功能保留")
        core_count += 1
    else:
        print("❌ 比例尺功能缺失")
    
    # 检查形状检测
    if 'detect_inner_shapes' in code:
        print("✅ 形状检测功能保留")
        core_count += 1
    else:
        print("❌ 形状检测功能缺失")
    
    # 检查高度测量
    if '"height":' in code and 'shape_height' in code:
        print("✅ 高度测量功能保留")
        core_count += 1
    else:
        print("❌ 高度测量功能缺失")
    
    # 检查实际尺寸显示
    if 'actual_width' in code and 'actual_height' in code:
        print("✅ 实际尺寸显示保留")
        core_count += 1
    else:
        print("❌ 实际尺寸显示缺失")
    
    print(f"📊 核心功能完整度: {core_count}/4")
    return core_count >= 3

def check_spelling_fixes():
    """检查拼写错误是否修复"""
    print("\n🔍 检查拼写错误...")
    
    with open('maixcam_shape_detection.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    # 检查是否还有拼写错误
    if 'heigth' in code:
        print("❌ 仍存在拼写错误：'heigth'")
        return False
    else:
        print("✅ 无拼写错误")
        return True

def show_current_features():
    """显示当前版本的功能"""
    print("\n📋 当前版本功能:")
    print("=" * 50)
    print("✅ 保留的核心功能:")
    print("   1. 外边框检测（绿色矩形）")
    print("   2. 内部形状识别（三角形、正方形、圆形）")
    print("   3. 形状高度测量（像素+毫米）")
    print("   4. 比例尺自动标定（21.0mm基准）")
    print("   5. 实时尺寸显示（宽度+高度）")
    print("   6. 比例尺实时显示")
    print()
    print("❌ 已移除的功能:")
    print("   1. 摄像头到目标物距离计算")
    print("   2. 焦距参数配置")
    print("   3. 距离显示界面")
    print()
    print("🎯 界面显示信息:")
    print("   • 左上角: 检测状态 (have/none)")
    print("   • 第二行: 比例尺 (Scale: X.XXXmm/px)")
    print("   • 外边框右侧: 宽高 (像素+毫米)")
    print("   • 内部形状旁: 实际高度 (毫米)")
    print("=" * 50)

def show_usage_guide():
    """显示使用指南"""
    print("\n📋 使用指南:")
    print("=" * 50)
    print("🎮 运行程序:")
    print("   python maixcam_shape_detection.py")
    print()
    print("🔧 标定方法:")
    print("   1. 确保外边框最小边实际长度为21.0mm")
    print("   2. 将标准物体放在摄像头前")
    print("   3. 程序自动计算比例尺")
    print("   4. 实时显示所有检测到的形状尺寸")
    print()
    print("💡 注意事项:")
    print("   • 保持稳定的光照条件")
    print("   • 确保物体边缘清晰可见")
    print("   • 标准物体尺寸要准确")
    print("=" * 50)

if __name__ == "__main__":
    print("🚀 MaixCAM形状检测代码恢复验证")
    print("=" * 50)
    
    all_passed = True
    
    # 语法检查
    if not test_code_syntax():
        all_passed = False
    
    # 移除功能检查
    if not check_removed_features():
        print("⚠️  部分有问题的功能可能未完全移除")
    
    # 核心功能检查
    if not check_core_features():
        all_passed = False
    
    # 拼写检查
    if not check_spelling_fixes():
        all_passed = False
    
    # 显示当前功能
    show_current_features()
    
    # 使用指南
    show_usage_guide()
    
    if all_passed:
        print("\n🎉 代码已成功恢复到稳定版本！")
        print("💡 提示：现在可以安全地在MaixCAM设备上运行")
        print("🔧 如需添加新功能，建议先在此稳定版本基础上测试")
    else:
        print("\n❌ 恢复过程中发现问题，请检查")
        sys.exit(1)
