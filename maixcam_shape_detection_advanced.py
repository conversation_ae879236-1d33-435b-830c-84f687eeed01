#!/usr/bin/env python3
"""
MaixCAM高级形状检测程序
使用OpenCV兼容层实现完整的形状检测功能

功能：
1. 完整的边缘检测和轮廓查找
2. 精确的形状识别和分类
3. 高度测量和可视化
4. 性能优化和错误处理

作者：Alex (Engineer)
版权：米醋电子工作室
"""

import math
import numpy as np
from maix import camera, display, image, app, time

# 尝试导入OpenCV进行高级图像处理
try:
    import cv2
    OPENCV_AVAILABLE = True
    print("OpenCV可用，启用高级图像处理功能")
except ImportError:
    OPENCV_AVAILABLE = False
    print("OpenCV不可用，使用基础图像处理功能")

class AdvancedShapeDetector:
    """高级形状检测器类"""
    
    def __init__(self, width=640, height=480):
        """初始化检测器"""
        self.width = width
        self.height = height
        self.cam = camera.Camera(width, height)
        self.disp = display.Display()
        
        # 检测参数
        self.min_area = 2000
        self.min_inner_area = 300
        self.gaussian_kernel = 5
        self.canny_low = 50
        self.canny_high = 150
        
        # 性能统计
        self.frame_count = 0
        self.start_time = time.ticks_ms()
        
        print("MaixCAM高级形状检测器初始化完成")
        print(f"分辨率: {width}x{height}")
        print(f"OpenCV支持: {'是' if OPENCV_AVAILABLE else '否'}")
    
    def maix_to_cv2(self, maix_img):
        """将MaixPy图像转换为OpenCV格式"""
        if not OPENCV_AVAILABLE:
            return None
            
        # 获取图像数据
        img_data = maix_img.to_bytes()
        
        if maix_img.format() == image.Format.FMT_RGB888:
            # RGB格式转换
            np_array = np.frombuffer(img_data, dtype=np.uint8)
            np_array = np_array.reshape((maix_img.height(), maix_img.width(), 3))
            cv_img = cv2.cvtColor(np_array, cv2.COLOR_RGB2BGR)
        elif maix_img.format() == image.Format.FMT_GRAYSCALE:
            # 灰度格式转换
            np_array = np.frombuffer(img_data, dtype=np.uint8)
            cv_img = np_array.reshape((maix_img.height(), maix_img.width()))
        else:
            # 其他格式先转换为RGB
            rgb_img = maix_img.to_format(image.Format.FMT_RGB888)
            return self.maix_to_cv2(rgb_img)
        
        return cv_img
    
    def cv2_to_maix(self, cv_img):
        """将OpenCV图像转换为MaixPy格式"""
        if not OPENCV_AVAILABLE:
            return None
            
        if len(cv_img.shape) == 3:  # 彩色图像
            rgb_img = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
            return image.Image(cv_img.shape[1], cv_img.shape[0], 
                              image.Format.FMT_RGB888, rgb_img.tobytes())
        else:  # 灰度图像
            return image.Image(cv_img.shape[1], cv_img.shape[0], 
                              image.Format.FMT_GRAYSCALE, cv_img.tobytes())
    
    def detect_outer_rectangle_opencv(self, cv_gray):
        """使用OpenCV检测外边框"""
        # 高斯模糊减少噪声
        blurred = cv2.GaussianBlur(cv_gray, (self.gaussian_kernel, self.gaussian_kernel), 0)
        
        # 边缘检测
        edges = cv2.Canny(blurred, self.canny_low, self.canny_high)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        max_area = 0
        best_rect = None
        x, y, w, h = None, None, None, None
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < self.min_area:
                continue
                
            # 近似轮廓为多边形
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # 检查是否为四边形
            if len(approx) == 4:
                rect_x, rect_y, rect_w, rect_h = cv2.boundingRect(approx)
                aspect_ratio = float(rect_w) / rect_h
                
                # 过滤过于狭长的形状
                if 0.5 < aspect_ratio < 2.0 and area > max_area:
                    max_area = area
                    best_rect = approx.reshape(-1, 2)  # 转换为(N,2)格式
                    x, y, w, h = rect_x, rect_y, rect_w, rect_h
        
        return best_rect, x, y, w, h
    
    def detect_inner_shapes_opencv(self, inner_roi, inner_x, inner_y):
        """使用OpenCV检测内部形状"""
        detected_shapes = []
        
        if inner_roi.size == 0:
            return detected_shapes
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(inner_roi, (self.gaussian_kernel, self.gaussian_kernel), 0)
        
        # 边缘检测
        edges = cv2.Canny(blurred, self.canny_low, self.canny_high)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < self.min_inner_area:
                continue
            
            # 轮廓近似
            perimeter = cv2.arcLength(cnt, True)
            epsilon = 0.03 * perimeter
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            vertices = len(approx)
            
            # 计算形状中心
            M = cv2.moments(cnt)
            if M["m00"] == 0:
                continue
            
            cX = int(M["m10"] / M["m00"]) + inner_x
            cY = int(M["m01"] / M["m00"]) + inner_y
            
            # 形状判断
            shape = "none"
            shape_height = 0
            top_point = (0, 0)
            bottom_point = (0, 0)
            
            if vertices == 3:  # 三角形
                # 检查是否为等边三角形
                side_lengths = []
                for i in range(3):
                    p1 = approx[i][0]
                    p2 = approx[(i+1)%3][0]
                    length = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                    side_lengths.append(length)
                
                max_side = max(side_lengths)
                ratios = [s/max_side for s in side_lengths]
                
                if all(r > 0.8 for r in ratios):
                    shape = "Triangle"
                    # 找到最高和最低点
                    points = approx.reshape(-1, 2)
                    top_idx = np.argmin(points[:, 1])
                    bottom_idx = np.argmax(points[:, 1])
                    
                    top_point = (points[top_idx][0] + inner_x, points[top_idx][1] + inner_y)
                    bottom_point = (points[bottom_idx][0] + inner_x, points[bottom_idx][1] + inner_y)
                    shape_height = int(np.sqrt((top_point[0] - bottom_point[0])**2 + 
                                             (top_point[1] - bottom_point[1])**2))
            
            elif vertices == 4:  # 四边形
                x_cnt, y_cnt, w_cnt, h_cnt = cv2.boundingRect(approx)
                aspect = w_cnt / h_cnt
                if 0.9 < aspect < 1.1:
                    shape = "Square"
                    top_point = (cX, y_cnt + inner_y)
                    bottom_point = (cX, y_cnt + h_cnt + inner_y)
                    shape_height = h_cnt
            
            else:  # 可能是圆形
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                if circularity > 0.8:
                    shape = "Circle"
                    (circle_x, circle_y), radius = cv2.minEnclosingCircle(cnt)
                    circle_x = int(circle_x) + inner_x
                    circle_y = int(circle_y) + inner_y
                    radius = int(radius)
                    
                    top_point = (circle_x, circle_y - radius)
                    bottom_point = (circle_x, circle_y + radius)
                    shape_height = radius * 2
            
            # 保存形状信息
            if shape != "none":
                detected_shapes.append({
                    "name": shape,
                    "contour": cnt,
                    "height": shape_height,
                    "top_point": top_point,
                    "bottom_point": bottom_point,
                    "center": (cX, cY)
                })
        
        return detected_shapes
    
    def detect_outer_rectangle_basic(self, maix_img):
        """使用基础方法检测外边框"""
        # 转换为灰度图
        if maix_img.format() != image.Format.FMT_GRAYSCALE:
            gray_img = maix_img.to_format(image.Format.FMT_GRAYSCALE)
        else:
            gray_img = maix_img
        
        # 使用色块检测
        thresholds = [[0, 100, -128, 127, -128, 127]]
        blobs = gray_img.find_blobs(thresholds, 
                                   pixels_threshold=self.min_area,
                                   area_threshold=self.min_area)
        
        max_area = 0
        best_rect = None
        x, y, w, h = None, None, None, None
        
        for blob in blobs:
            blob_area = blob[4]
            if blob_area > max_area:
                max_area = blob_area
                x, y, w, h = blob[0], blob[1], blob[2], blob[3]
                best_rect = [(x, y), (x + w, y), (x + w, y + h), (x, y + h)]
        
        return best_rect, x, y, w, h
    
    def process_frame(self, frame):
        """处理单帧图像"""
        display_img = frame.copy()
        
        if OPENCV_AVAILABLE:
            # 使用OpenCV处理
            cv_img = self.maix_to_cv2(frame)
            if cv_img is None:
                return display_img
            
            # 转换为灰度图
            if len(cv_img.shape) == 3:
                cv_gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
            else:
                cv_gray = cv_img
            
            # 检测外边框
            best_rect, x, y, w, h = self.detect_outer_rectangle_opencv(cv_gray)
        else:
            # 使用基础方法
            best_rect, x, y, w, h = self.detect_outer_rectangle_basic(frame)
        
        # 显示状态
        status_text = "none"
        status_color = image.Color.from_rgb(255, 0, 0)
        
        if best_rect is not None:
            status_text = "have"
            status_color = image.Color.from_rgb(0, 255, 0)

            # 绘制矩形
            for i in range(len(best_rect)):
                p1 = best_rect[i]
                p2 = best_rect[(i + 1) % len(best_rect)]
                display_img.draw_line(int(p1[0]), int(p1[1]),
                                    int(p2[0]), int(p2[1]), status_color, 2)

            # 计算并显示外边框的长宽
            if x is not None and y is not None and w is not None and h is not None:
                # 在外边框右上角显示宽度
                width_text = f"W:{w}px"
                display_img.draw_string(x + w + 5, y - 10, width_text,
                                      image.Color.from_rgb(0, 255, 0))

                # 在外边框右侧中间显示高度
                height_text = f"H:{h}px"
                display_img.draw_string(x + w + 5, y + h//2, height_text,
                                      image.Color.from_rgb(0, 255, 0))
            
            # 处理内部区域
            if OPENCV_AVAILABLE and x is not None:
                inner_x = x + int(w * 0.1)
                inner_y = y + int(h * 0.1)
                inner_w = int(w * 0.8)
                inner_h = int(h * 0.8)
                
                # 提取内部区域
                cv_img = self.maix_to_cv2(frame)
                if cv_img is not None:
                    if len(cv_img.shape) == 3:
                        cv_gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
                    else:
                        cv_gray = cv_img
                    
                    inner_roi = cv_gray[inner_y:inner_y+inner_h, inner_x:inner_x+inner_w]
                    
                    # 检测内部形状
                    detected_shapes = self.detect_inner_shapes_opencv(inner_roi, inner_x, inner_y)
                    
                    # 绘制形状
                    for shape_info in detected_shapes:
                        # 绘制高度线
                        display_img.draw_line(shape_info["top_point"][0], shape_info["top_point"][1],
                                            shape_info["bottom_point"][0], shape_info["bottom_point"][1],
                                            image.Color.from_rgb(255, 255, 0), 2)
                        
                        # 显示形状名称和高度
                        display_img.draw_string(shape_info["center"][0] - 30, shape_info["center"][1],
                                              shape_info["name"], image.Color.from_rgb(255, 0, 0))
                        
                        mid_x = (shape_info["top_point"][0] + shape_info["bottom_point"][0]) // 2
                        mid_y = (shape_info["top_point"][1] + shape_info["bottom_point"][1]) // 2
                        display_img.draw_string(mid_x + 20, mid_y,
                                              f"{shape_info['height']}px", 
                                              image.Color.from_rgb(255, 255, 0))
                        
                        # 标记端点
                        display_img.draw_circle(shape_info["top_point"][0], shape_info["top_point"][1],
                                              5, image.Color.from_rgb(0, 255, 255), -1)
                        display_img.draw_circle(shape_info["bottom_point"][0], shape_info["bottom_point"][1],
                                              5, image.Color.from_rgb(0, 255, 255), -1)
        
        # 添加状态和性能信息
        display_img.draw_string(10, 30, status_text, status_color)
        
        # 计算FPS
        current_time = time.ticks_ms()
        elapsed = current_time - self.start_time
        if elapsed > 0:
            fps = self.frame_count * 1000 / elapsed
            display_img.draw_string(10, 60, f"FPS: {fps:.1f}", 
                                  image.Color.from_rgb(255, 255, 255))
        
        return display_img
    
    def run(self):
        """运行主循环"""
        print("开始高级形状检测...")
        
        try:
            while not app.need_exit():
                frame = self.cam.read()
                if frame is None:
                    continue
                
                processed_frame = self.process_frame(frame)
                self.disp.show(processed_frame)
                
                self.frame_count += 1
                
                if self.frame_count % 60 == 0:
                    current_time = time.ticks_ms()
                    elapsed = current_time - self.start_time
                    fps = self.frame_count * 1000 / elapsed if elapsed > 0 else 0
                    print(f"已处理 {self.frame_count} 帧, 平均FPS: {fps:.1f}")
                
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行出错: {e}")
        finally:
            print("程序结束")


def main():
    """主函数"""
    print("=" * 50)
    print("MaixCAM高级形状检测程序")
    print("版权所有：米醋电子工作室")
    print("=" * 50)
    
    detector = AdvancedShapeDetector(width=640, height=480)
    detector.run()


if __name__ == "__main__":
    main()
